{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "run portal",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${env:GOPATH}/src/tensorsecurity-rd/tensornavigator/cmd/portal/main.go",
      "env": {
        "GOPATH": "${env:GOPATH}",
        "GOROOT": "${env:GOROOT}",
        "PORTAL_CONFIG_PATH": "${env:GOPATH}/src/tensorsecurity-rd/tensornavigator/cmd/portal/component/config/portal-config.yaml",
        "RDB_USER": "${env:RDB_USER}",
        "RDB_PASSWORD": "${env:RDB_PASSWORD}",
        "RDB_HOST": "${env:RDB_HOST}",
        "RDB_READONLY_HOST": "${env:RDB_READONLY_HOST}",
        "RDB_PORT": "${env:RDB_PORT}",
        "RDB_DBNAME": "${env:RDB_DBNAME}",
        "RDB_SSLMODE": "${env:RDB_SSLMODE}",
        "LOG_LEVEL": "${env:LOG_LEVEL}"
      },
      "args": [
        "--config-path=${env:GOPATH}/src/tensorsecurity-rd/tensornavigator/cmd/portal/component/config/portal-config.yaml"
      ],
      "showLog": true
    },
    {
      "name": "update codesec project",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${env:GOPATH}/src/tensorsecurity-rd/tensornavigator/cmd/portal/component/codesec/updatepoject/main.go",
      "env": {
        "GOPATH": "${env:GOPATH}",
        "GOROOT": "${env:GOROOT}",
        "PORTAL_CONFIG_PATH": "${env:GOPATH}/src/tensorsecurity-rd/tensornavigator/cmd/portal/component/config/portal-config.yaml",
        "RDB_USER": "${env:RDB_USER}",
        "RDB_PASSWORD": "${env:RDB_PASSWORD}",
        "RDB_HOST": "${env:RDB_HOST}",
        "RDB_READONLY_HOST": "${env:RDB_READONLY_HOST}",
        "RDB_PORT": "${env:RDB_PORT}",
        "RDB_DBNAME": "${env:RDB_DBNAME}",
        "RDB_SSLMODE": "${env:RDB_SSLMODE}",
        "LOG_LEVEL": "${env:LOG_LEVEL}"
      },
      "args": [
        // "--config-path=${env:GOPATH}/src/tensorsecurity-rd/tensornavigator/cmd/portal/component/config/portal-config.yaml"
      ],
      "showLog": true
    },
    {
      "name": "Debug Test",
      "type": "go",
      "request": "launch",
      "mode": "test",
      "program": "${fileDirname}",
      "showLog": true
    },
    {
      "name": "Attach to Process",
      "type": "go",
      "request": "attach",
      "mode": "local",
      "processId": 0
    }
  ]
}
