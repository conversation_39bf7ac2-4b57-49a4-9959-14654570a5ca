package store

import (
	"context"
	"time"

	model "gitlab.com/piccolo_su/vegeta/cmd/portal/model"
	"gitlab.com/security-rd/go-pkg/databases"
)

type CodeSecScanResultStore interface {
	SearchCodeSecScanResult(ctx context.Context, param model.SearchCodeSecScanResultParam) ([]*model.CodeSecScanResult, int64, error)
	CreateCodeSecScanResult(ctx context.Context, result *model.CodeSecScanResult) error
	UpdateCodeSecScanResult(ctx context.Context, param model.UpdateCodeSecScanResultParam) error
	DeleteCodeSecScanResult(ctx context.Context, id int64) error
	GetLatestCodeSecScanResult(ctx context.Context, projectID int64) (*model.CodeSecScanResult, error)
}

type codeSecScanResultStore struct {
	db *databases.RDBInstance
}

func NewCodeSecScanResultStore(db *databases.RDBInstance) CodeSecScanResultStore {
	return &codeSecScanResultStore{db: db}
}

func (s *codeSecScanResultStore) SearchCodeSecScanResult(ctx context.Context, param model.SearchCodeSecScanResultParam) ([]*model.CodeSecScanResult, int64, error) {
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	db := s.db.Get().WithContext(ctx).Model(&model.CodeSecScanResult{})

	if param.ID > 0 {
		db = db.Where("id = ?", param.ID)
	}
	if param.ProjectID > 0 {
		db = db.Where("project_id = ?", param.ProjectID)
	}
	if param.CodeSecUUID != "" {
		db = db.Where("codesec_uuid = ?", param.CodeSecUUID)
	}
	if param.ScanID != "" {
		db = db.Where("scan_id = ?", param.ScanID)
	}

	var results []*model.CodeSecScanResult
	var cnt int64
	if err := db.Count(&cnt).Error; err != nil {
		return nil, 0, err
	}

	if param.Filter != nil {
		db = db.Offset(int(param.Filter.Offset)).Limit(int(param.Filter.Limit))
		if param.Filter.SortField != "" {
			if param.Filter.SortDesc {
				db = db.Order(param.Filter.SortField + " DESC")
			} else {
				db = db.Order(param.Filter.SortField)
			}
		}
	}

	if err := db.Find(&results).Error; err != nil {
		return nil, 0, err
	}

	return results, cnt, nil
}

func (s *codeSecScanResultStore) CreateCodeSecScanResult(ctx context.Context, result *model.CodeSecScanResult) error {
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	return s.db.Get().WithContext(ctx).Create(result).Error
}

func (s *codeSecScanResultStore) UpdateCodeSecScanResult(ctx context.Context, param model.UpdateCodeSecScanResultParam) error {
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	return s.db.Get().WithContext(ctx).Model(&model.CodeSecScanResult{}).Where("id = ?", param.ID).Updates(param.Updater).Error
}

func (s *codeSecScanResultStore) DeleteCodeSecScanResult(ctx context.Context, id int64) error {
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	return s.db.Get().WithContext(ctx).Delete(&model.CodeSecScanResult{}, id).Error
}

func (s *codeSecScanResultStore) GetLatestCodeSecScanResult(ctx context.Context, projectID int64) (*model.CodeSecScanResult, error) {
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	var result model.CodeSecScanResult
	err := s.db.Get().WithContext(ctx).
		Where("project_id = ?", projectID).
		Order("scan_time DESC").
		First(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}
