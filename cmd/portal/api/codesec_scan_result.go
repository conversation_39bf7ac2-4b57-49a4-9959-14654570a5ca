package api

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/service"
	"gitlab.com/piccolo_su/vegeta/pkg/response"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
)

type CodeSecScanResultHandler struct {
	service service.CodeSecScanResultService
}

func NewCodeSecScanResultHandler(service service.CodeSecScanResultService) *CodeSecScanResultHandler {
	return &CodeSecScanResultHandler{service: service}
}

// GetCodeSecScanResult 获取扫描结果
func (h *CodeSecScanResultHandler) GetCodeSecScanResult(ctx *gin.Context) {
	id := util.GetInt64FromQuery(ctx, "id")
	if id <= 0 {
		response.JSONError(ctx, fmt.Errorf("id is required"))
		return
	}

	result, err := h.service.GetCodeSecScanResult(ctx, id)
	if err != nil {
		response.JSONError(ctx, err)
		return
	}

	response.JSONOK(ctx, response.WithItem(result.ToResponse()))
}

// GetLatestCodeSecScanResult 获取最新扫描结果
func (h *CodeSecScanResultHandler) GetLatestCodeSecScanResult(ctx *gin.Context) {
	projectID := util.GetInt64FromQuery(ctx, "projectId")
	if projectID <= 0 {
		response.JSONError(ctx, fmt.Errorf("projectId is required"))
		return
	}

	result, err := h.service.GetLatestCodeSecScanResult(ctx, projectID)
	if err != nil {
		response.JSONError(ctx, err)
		return
	}

	response.JSONOK(ctx, response.WithItem(result.ToResponse()))
}

// FetchAndSaveCodeSecScanResult 获取并保存扫描结果
func (h *CodeSecScanResultHandler) FetchAndSaveCodeSecScanResult(ctx *gin.Context) {
	projectID := util.GetInt64FromQuery(ctx, "projectId")
	if projectID <= 0 {
		response.JSONError(ctx, fmt.Errorf("projectId is required"))
		return
	}

	codesecUUID := ctx.Query("codesecUUID")
	if codesecUUID == "" {
		response.JSONError(ctx, fmt.Errorf("codesecUUID is required"))
		return
	}

	scanID := ctx.Query("scanId")
	if scanID == "" {
		response.JSONError(ctx, fmt.Errorf("scanId is required"))
		return
	}

	result, err := h.service.FetchAndSaveCodeSecScanResult(ctx, projectID, codesecUUID, scanID)
	if err != nil {
		response.JSONError(ctx, err)
		return
	}

	response.JSONOK(ctx, response.WithItem(result.ToResponse()))
}

// StartCodeSecScan 启动扫描
func (h *CodeSecScanResultHandler) StartCodeSecScan(ctx *gin.Context) {
	projectID := util.GetInt64FromQuery(ctx, "projectId")
	if projectID <= 0 {
		response.JSONError(ctx, fmt.Errorf("projectId is required"))
		return
	}

	codesecUUID := ctx.Query("codesecUUID")
	if codesecUUID == "" {
		response.JSONError(ctx, fmt.Errorf("codesecUUID is required"))
		return
	}

	scanID, err := h.service.StartCodeSecScan(ctx, projectID, codesecUUID)
	if err != nil {
		response.JSONError(ctx, err)
		return
	}

	response.JSONOK(ctx, response.WithItem(map[string]string{"scanId": scanID}))
}
