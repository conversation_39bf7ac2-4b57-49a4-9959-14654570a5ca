package service

import (
	"context"
	"fmt"
	"time"

	model "gitlab.com/piccolo_su/vegeta/cmd/portal/model"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/store"
)

type CodeSecScanResultService interface {
	SearchCodeSecScanResult(ctx context.Context, param model.SearchCodeSecScanResultParam) ([]*model.CodeSecScanResult, int64, error)
	GetCodeSecScanResult(ctx context.Context, id int64) (*model.CodeSecScanResult, error)
	CreateCodeSecScanResult(ctx context.Context, result *model.CodeSecScanResult) error
	UpdateCodeSecScanResult(ctx context.Context, id int64, result *model.CodeSecScanResult) error
	DeleteCodeSecScanResult(ctx context.Context, id int64) error
	GetLatestCodeSecScanResult(ctx context.Context, projectID int64) (*model.CodeSecScanResult, error)
	FetchAndSaveCodeSecScanResult(ctx context.Context, projectID int64, codesecUUID string, scanID string) (*model.CodeSecScanResult, error)
	StartCodeSecScan(ctx context.Context, projectID int64, codesecUUID string) (string, error)
}

type codeSecScanResultService struct {
	store      store.CodeSecScanResultStore
	codesecURL string
}

func NewCodeSecScanResultService(
	store store.CodeSecScanResultStore,
	projectStore store.ProjectStore,
	userStore interface{},
	log interface{},
	codesecURL string,
) CodeSecScanResultService {
	return &codeSecScanResultService{
		store:      store,
		codesecURL: codesecURL,
	}
}

func (s *codeSecScanResultService) SearchCodeSecScanResult(ctx context.Context, param model.SearchCodeSecScanResultParam) ([]*model.CodeSecScanResult, int64, error) {
	return s.store.SearchCodeSecScanResult(ctx, param)
}

func (s *codeSecScanResultService) GetCodeSecScanResult(ctx context.Context, id int64) (*model.CodeSecScanResult, error) {
	results, _, err := s.store.SearchCodeSecScanResult(ctx, model.SearchCodeSecScanResultParam{ID: id})
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, fmt.Errorf("scan result not found")
	}
	return results[0], nil
}

func (s *codeSecScanResultService) CreateCodeSecScanResult(ctx context.Context, result *model.CodeSecScanResult) error {
	return s.store.CreateCodeSecScanResult(ctx, result)
}

func (s *codeSecScanResultService) UpdateCodeSecScanResult(ctx context.Context, id int64, result *model.CodeSecScanResult) error {
	result.ID = id
	updater := map[string]interface{}{
		"project_id":       result.ProjectID,
		"codesec_uuid":     result.CodeSecUUID,
		"scan_id":          result.ScanID,
		"scan_status":      result.ScanStatus,
		"vul_critical_num": result.VulCriticalNum,
		"vul_high_num":     result.VulHighNum,
		"vul_medium_num":   result.VulMediumNum,
		"vul_low_num":      result.VulLowNum,
		"vul_no_risk_num":  result.VulNoRiskNum,
		"vul_total_num":    result.VulTotalNum,
		"scan_result_json": result.ScanResultJSON,
		"scan_time":        result.ScanTime,
	}
	return s.store.UpdateCodeSecScanResult(ctx, model.UpdateCodeSecScanResultParam{ID: id, Updater: updater})
}

func (s *codeSecScanResultService) DeleteCodeSecScanResult(ctx context.Context, id int64) error {
	return s.store.DeleteCodeSecScanResult(ctx, id)
}

func (s *codeSecScanResultService) GetLatestCodeSecScanResult(ctx context.Context, projectID int64) (*model.CodeSecScanResult, error) {
	return s.store.GetLatestCodeSecScanResult(ctx, projectID)
}

func (s *codeSecScanResultService) FetchAndSaveCodeSecScanResult(ctx context.Context, projectID int64, codesecUUID string, scanID string) (*model.CodeSecScanResult, error) {
	// 简化实现，仅创建一个示例结果
	result := &model.CodeSecScanResult{
		ProjectID:      projectID,
		CodeSecUUID:    codesecUUID,
		ScanID:         scanID,
		ScanStatus:     model.CodeSecScanStatusCompleted,
		VulCriticalNum: 1,
		VulHighNum:     2,
		VulMediumNum:   3,
		VulLowNum:      4,
		VulNoRiskNum:   5,
		VulTotalNum:    15,
		ScanResultJSON: "{}",
		ScanTime:       time.Now().UnixMilli(),
	}

	// 创建新的扫描结果
	err := s.CreateCodeSecScanResult(ctx, result)
	if err != nil {
		return nil, fmt.Errorf("create codesec scan result failed: %v", err)
	}

	return result, nil
}

func (s *codeSecScanResultService) StartCodeSecScan(ctx context.Context, projectID int64, codesecUUID string) (string, error) {
	// 简化实现，仅返回一个示例扫描ID
	scanID := fmt.Sprintf("scan-%d-%s-%d", projectID, codesecUUID, time.Now().Unix())

	// 创建扫描结果记录
	result := &model.CodeSecScanResult{
		ProjectID:      projectID,
		CodeSecUUID:    codesecUUID,
		ScanID:         scanID,
		ScanStatus:     model.CodeSecScanStatusScanning,
		VulCriticalNum: 0,
		VulHighNum:     0,
		VulMediumNum:   0,
		VulLowNum:      0,
		VulNoRiskNum:   0,
		VulTotalNum:    0,
		ScanResultJSON: "{}",
		ScanTime:       time.Now().UnixMilli(),
	}

	// 保存扫描结果
	err := s.CreateCodeSecScanResult(ctx, result)
	if err != nil {
		return "", fmt.Errorf("create codesec scan result failed: %v", err)
	}

	return scanID, nil
}
