package model

import (
	"fmt"
	"strings"
	"time"

	imagesecModel "gitlab.com/piccolo_su/vegeta/pkg/model/imagesec"
)

// CodeSecScanResult 代码扫描结果
type CodeSecScanResult struct {
	ID            int64  `gorm:"primary_key;AUTO_INCREMENT" json:"id"`
	ProjectID     int64  `gorm:"column:project_id" json:"projectId"`
	CodeSecUUID   string `gorm:"column:codesec_uuid" json:"codesecUUID"`
	ScanID        string `gorm:"column:scan_id" json:"scanId"`
	ScanStatus    int    `gorm:"column:scan_status" json:"scanStatus"` // 0: 未扫描, 1: 扫描中, 2: 扫描完成, 3: 扫描失败
	VulCriticalNum int   `gorm:"column:vul_critical_num" json:"vulCriticalNum"`
	VulHighNum    int    `gorm:"column:vul_high_num" json:"vulHighNum"`
	VulMediumNum  int    `gorm:"column:vul_medium_num" json:"vulMediumNum"`
	VulLowNum     int    `gorm:"column:vul_low_num" json:"vulLowNum"`
	VulNoRiskNum  int    `gorm:"column:vul_no_risk_num" json:"vulNoRiskNum"`
	VulTotalNum   int    `gorm:"column:vul_total_num" json:"vulTotalNum"`
	ScanResultJSON string `gorm:"column:scan_result_json" json:"scanResultJson"`
	ScanTime      int64  `gorm:"column:scan_time" json:"scanTime"`
	CreatedAt     int64  `gorm:"autoCreateTime:milli;column:created_at" json:"createdAt"` // milliseconds
	UpdatedAt     int64  `gorm:"autoUpdateTime:milli;column:updated_at" json:"updatedAt"` // milliseconds
}

func (c *CodeSecScanResult) TableName() string {
	return "portal_codesec_scan_result"
}

// CodeSecScanStatus 扫描状态
const (
	CodeSecScanStatusNotScanned = 0 // 未扫描
	CodeSecScanStatusScanning   = 1 // 扫描中
	CodeSecScanStatusCompleted  = 2 // 扫描完成
	CodeSecScanStatusFailed     = 3 // 扫描失败
)

// SearchCodeSecScanResultParam 查询参数
type SearchCodeSecScanResultParam struct {
	ID          int64
	ProjectID   int64
	CodeSecUUID string
	ScanID      string
	Filter      *imagesec.Filter
}

// UpdateCodeSecScanResultParam 更新参数
type UpdateCodeSecScanResultParam struct {
	ID      int64
	Updater map[string]interface{}
}

// CodeSecScanResultResponse 扫描结果响应
type CodeSecScanResultResponse struct {
	ID            int64  `json:"id"`
	ProjectID     int64  `json:"projectId"`
	CodeSecUUID   string `json:"codesecUUID"`
	ScanID        string `json:"scanId"`
	ScanStatus    int    `json:"scanStatus"`
	VulCriticalNum int   `json:"vulCriticalNum"`
	VulHighNum    int    `json:"vulHighNum"`
	VulMediumNum  int    `json:"vulMediumNum"`
	VulLowNum     int    `json:"vulLowNum"`
	VulNoRiskNum  int    `json:"vulNoRiskNum"`
	VulTotalNum   int    `json:"vulTotalNum"`
	ScanTime      int64  `json:"scanTime"`
	ScanTimeStr   string `json:"scanTimeStr"`
	CreatedAt     int64  `json:"createdAt"`
	UpdatedAt     int64  `json:"updatedAt"`
}

// ToResponse 转换为响应
func (c *CodeSecScanResult) ToResponse() *CodeSecScanResultResponse {
	return &CodeSecScanResultResponse{
		ID:            c.ID,
		ProjectID:     c.ProjectID,
		CodeSecUUID:   c.CodeSecUUID,
		ScanID:        c.ScanID,
		ScanStatus:    c.ScanStatus,
		VulCriticalNum: c.VulCriticalNum,
		VulHighNum:    c.VulHighNum,
		VulMediumNum:  c.VulMediumNum,
		VulLowNum:     c.VulLowNum,
		VulNoRiskNum:  c.VulNoRiskNum,
		VulTotalNum:   c.VulTotalNum,
		ScanTime:      c.ScanTime,
		ScanTimeStr:   time.UnixMilli(c.ScanTime).Format("2006-01-02 15:04:05"),
		CreatedAt:     c.CreatedAt,
		UpdatedAt:     c.UpdatedAt,
	}
}
