package vulnList

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"gitlab.com/piccolo_su/vegeta/cmd/portal/utils"
	"gitlab.com/security-rd/go-pkg/logging"
)

// VulnListParam 分页查询漏洞列表参数
type VulnListParam struct {
	AccessKey         string `json:"accessKey"`
	AccessSecret      string `json:"accessSecret"`
	ProjectUuid       string `json:"projectUuid"`
	AppId             string `json:"appId"`
	VulDataId         string `json:"vulDataId"`
	RecordId          string `json:"recordId,omitempty"`
	VulFlagType       int    `json:"vulFlagType,omitempty"`
	Type              int    `json:"type,omitempty"`
	PageCurrent       int    `json:"pageCurrent,omitempty"`
	PageSize          int    `json:"pageSize,omitempty"`
	PermissionOrgUuid string `json:"permissionOrgUuid,omitempty"`
	Signer            string `json:"signer,omitempty"`
	TagIdList         []int  `json:"tagIdList,omitempty"`
}

// VulnListResponse 分页查询漏洞列表响应
type VulnListResponse struct {
	Status  bool     `json:"status"`
	Message string   `json:"message"`
	Data    PageData `json:"data"`
}

// PageData 分页数据
type PageData struct {
	Total       int        `json:"total"`
	PageTotal   int        `json:"pageTotal"`
	PageCurrent int        `json:"pageCurrent"`
	PageSize    int        `json:"pageSize"`
	VulTraces   []VulTrace `json:"vulTraces"`
}

// VulTrace 漏洞信息
type VulTrace struct {
	VulId        string     `json:"vulId"`
	VulTypeId    string     `json:"vulTypeId"`
	Filename     string     `json:"filename"`
	RowNum       string     `json:"rowNum"`
	VulDataId    string     `json:"vulDataId"`
	VulFlag      string     `json:"vulFlag"`
	Name         string     `json:"name"`
	TagId        string     `json:"tagId"`
	Tag          TagInfo    `json:"tag"`
	RiskId       int        `json:"riskId"`
	Risk         RiskInfo   `json:"risk"`
	NodeList     []NodeInfo `json:"nodeList"`
	Signer       string     `json:"signer"`
	CodingNodeId int        `json:"codingNodeId,omitempty"`
}

// TagInfo 标签信息
type TagInfo struct {
	Id         int    `json:"id"`
	NameLocale string `json:"nameLocale"`
}

// RiskInfo 风险等级信息
type RiskInfo struct {
	Id         int    `json:"id"`
	NameLocale string `json:"nameLocale"`
}

// NodeInfo 漏洞节点信息
type NodeInfo struct {
	Id        int    `json:"id"`
	OrgUuid   string `json:"orgUuid"`
	AppId     string `json:"appId"`
	RecordId  string `json:"recordId"`
	VulId     string `json:"vulId"`
	Filename  string `json:"filename"`
	LineCode  string `json:"lineCode"`
	LineNum   string `json:"lineNum"`
	CodeBlock string `json:"codeBlock"`
	IsSource  int    `json:"isSource"`
}

// GetVulnList 分页查询漏洞列表
func GetVulnList(baseUrl string, param VulnListParam) (*VulnListResponse, error) {
	// 定义请求URL
	apiURL := fmt.Sprintf("%s/cs/api/v4/project/%s/task/%s/getListDetailByVulDataId", baseUrl, param.ProjectUuid, param.AppId)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	pathValues := []string{param.ProjectUuid, param.AppId}
	header := utils.BuildV2Header(nil, param.AccessKey, param.AccessSecret, pathValues)
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 设置查询参数
	q := req.URL.Query()
	q.Add("vulDataId", param.VulDataId)
	if param.RecordId != "" {
		q.Add("recordId", param.RecordId)
	}
	if param.VulFlagType > 0 {
		q.Add("vulFlagType", strconv.Itoa(param.VulFlagType))
	}
	if param.Type > 0 {
		q.Add("type", strconv.Itoa(param.Type))
	}
	if param.PageCurrent > 0 {
		q.Add("pageCurrent", strconv.Itoa(param.PageCurrent))
	} else {
		q.Add("pageCurrent", "1") // 默认第1页
	}
	if param.PageSize > 0 {
		q.Add("pageSize", strconv.Itoa(param.PageSize))
	} else {
		q.Add("pageSize", "10") // 默认每页10条
	}
	if param.PermissionOrgUuid != "" {
		q.Add("permissionOrgUuid", param.PermissionOrgUuid)
	}
	if param.Signer != "" {
		q.Add("signer", param.Signer)
	}
	if len(param.TagIdList) > 0 {
		for _, tagId := range param.TagIdList {
			q.Add("tagIdList", strconv.Itoa(tagId))
		}
	}
	req.URL.RawQuery = q.Encode()

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &VulnListResponse{}
	all, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logging.Get().Info().Str("url", apiURL).Str("res", string(all)).Msg("Codesec vuln list resp")
	if err := json.Unmarshal(all, response); err != nil {
		return nil, err
	}
	if !response.Status {
		return nil, fmt.Errorf("get vuln list failed: %s", response.Message)
	}
	return response, nil
}
