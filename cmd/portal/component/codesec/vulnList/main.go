package vulnList

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"gitlab.com/piccolo_su/vegeta/cmd/portal/utils"
	"gitlab.com/security-rd/go-pkg/logging"
)

// VulnListParam 分页查询漏洞列表参数
type VulnListParam struct {
	AccessKey         string `json:"accessKey"`                   // [必传] 用户API访问KEY
	AccessSecret      string `json:"accessSecret"`                // [必传] 用户API访问密钥
	ProjectUuid       string `json:"projectUuid"`                 // [必传] 项目UUID
	AppId             string `json:"appId"`                       // [必传] 扫描任务ID
	VulDataId         string `json:"vulDataId"`                   // [必传] 漏洞库ID
	RecordId          string `json:"recordId,omitempty"`          // [可选] 扫描记录ID
	VulFlagType       int    `json:"vulFlagType,omitempty"`       // [可选] 缺陷跟踪类型：1：新发现 2：复发
	Type              int    `json:"type,omitempty"`              // [可选] 查询类型：0：静态（默认）1：编码规范
	PageCurrent       int    `json:"pageCurrent,omitempty"`       // [可选] 当前页 默认1
	PageSize          int    `json:"pageSize,omitempty"`          // [可选] 每页大小 默认10
	PermissionOrgUuid string `json:"permissionOrgUuid,omitempty"` // [可选] 指定权限校验团队UUID
	Signer            string `json:"signer,omitempty"`            // [可选] 缺陷ID
	TagIdList         []int  `json:"tagIdList,omitempty"`         // [可选] 缺陷状态ID列表
}

// VulnListResponse 分页查询漏洞列表响应
type VulnListResponse struct {
	Status  bool     `json:"status"`
	Message string   `json:"message"`
	Data    PageData `json:"data"`
}

// PageData 分页数据
type PageData struct {
	Total       int        `json:"total"`
	PageTotal   int        `json:"pageTotal"`
	PageCurrent int        `json:"pageCurrent"`
	PageSize    int        `json:"pageSize"`
	VulTraces   []VulTrace `json:"vulTraces"`
}

// VulTrace 漏洞信息
type VulTrace struct {
	VulId        string     `json:"vulId"`
	VulTypeId    string     `json:"vulTypeId"`
	Filename     string     `json:"filename"`
	RowNum       string     `json:"rowNum"`
	VulDataId    string     `json:"vulDataId"`
	VulFlag      string     `json:"vulFlag"`
	Name         string     `json:"name"`
	TagId        string     `json:"tagId"`
	Tag          TagInfo    `json:"tag"`
	RiskId       int        `json:"riskId"`
	Risk         RiskInfo   `json:"risk"`
	NodeList     []NodeInfo `json:"nodeList"`
	Signer       string     `json:"signer"`
	CodingNodeId int        `json:"codingNodeId,omitempty"`
}

// TagInfo 标签信息
type TagInfo struct {
	Id         int    `json:"id"`
	NameLocale string `json:"nameLocale"`
}

// RiskInfo 风险等级信息
type RiskInfo struct {
	Id         int    `json:"id"`
	NameLocale string `json:"nameLocale"`
}

// NodeInfo 漏洞节点信息
type NodeInfo struct {
	Id        int    `json:"id"`
	OrgUuid   string `json:"orgUuid"`
	AppId     string `json:"appId"`
	RecordId  string `json:"recordId"`
	VulId     string `json:"vulId"`
	Filename  string `json:"filename"`
	LineCode  string `json:"lineCode"`
	LineNum   string `json:"lineNum"`
	CodeBlock string `json:"codeBlock"`
	IsSource  int    `json:"isSource"`
}

// GetVulnList 分页查询漏洞列表
func GetVulnList(baseUrl string, param VulnListParam) (*VulnListResponse, error) {
	// 验证必传参数
	if param.AccessKey == "" {
		return nil, fmt.Errorf("AccessKey is required")
	}
	if param.AccessSecret == "" {
		return nil, fmt.Errorf("AccessSecret is required")
	}
	if param.ProjectUuid == "" {
		return nil, fmt.Errorf("ProjectUuid is required")
	}
	if param.AppId == "" {
		return nil, fmt.Errorf("AppId is required")
	}
	if param.VulDataId == "" {
		return nil, fmt.Errorf("VulDataId is required")
	}

	// 定义请求URL
	apiURL := fmt.Sprintf("%s/cs/api/v4/project/%s/task/%s/getListDetailByVulDataId", baseUrl, param.ProjectUuid, param.AppId)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	pathValues := []string{param.ProjectUuid, param.AppId}
	header := utils.BuildV2Header(nil, param.AccessKey, param.AccessSecret, pathValues)
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 设置查询参数
	q := req.URL.Query()
	q.Add("vulDataId", param.VulDataId)
	if param.RecordId != "" {
		q.Add("recordId", param.RecordId)
	}
	if param.VulFlagType > 0 {
		q.Add("vulFlagType", strconv.Itoa(param.VulFlagType))
	}
	if param.Type > 0 {
		q.Add("type", strconv.Itoa(param.Type))
	}
	if param.PageCurrent > 0 {
		q.Add("pageCurrent", strconv.Itoa(param.PageCurrent))
	} else {
		q.Add("pageCurrent", "1") // 默认第1页
	}
	if param.PageSize > 0 {
		q.Add("pageSize", strconv.Itoa(param.PageSize))
	} else {
		q.Add("pageSize", "10") // 默认每页10条
	}
	if param.PermissionOrgUuid != "" {
		q.Add("permissionOrgUuid", param.PermissionOrgUuid)
	}
	if param.Signer != "" {
		q.Add("signer", param.Signer)
	}
	if len(param.TagIdList) > 0 {
		for _, tagId := range param.TagIdList {
			q.Add("tagIdList", strconv.Itoa(tagId))
		}
	}
	req.URL.RawQuery = q.Encode()

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &VulnListResponse{}
	all, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logging.Get().Info().Str("url", apiURL).Str("res", string(all)).Msg("Codesec vuln list resp")
	if err := json.Unmarshal(all, response); err != nil {
		return nil, err
	}
	if !response.Status {
		return nil, fmt.Errorf("get vuln list failed: %s", response.Message)
	}
	return response, nil
}

// ExampleGetVulnList 示例：如何使用分页查询漏洞列表接口
func ExampleGetVulnList() {
	// 设置必要参数
	param := VulnListParam{
		AccessKey:    "your_access_key",    // 替换为实际的访问密钥
		AccessSecret: "your_access_secret", // 替换为实际的访问密钥
		ProjectUuid:  "project_uuid",       // 替换为实际的项目UUID
		AppId:        "app_id",             // 替换为实际的扫描任务ID
		VulDataId:    "vul_data_id",        // 替换为实际的漏洞库ID

		// 以下是可选参数
		RecordId:    "record_id",    // 可选：扫描记录ID
		VulFlagType: 1,              // 可选：缺陷跟踪类型 1:新发现
		Type:        0,              // 可选：查询类型 0:静态
		PageCurrent: 1,              // 可选：当前页
		PageSize:    10,             // 可选：每页大小
		TagIdList:   []int{1, 2, 3}, // 可选：缺陷状态ID列表
	}

	// 调用接口
	baseUrl := "https://codesec.example.com" // 替换为实际的CodeSec API地址
	resp, err := GetVulnList(baseUrl, param)
	if err != nil {
		fmt.Printf("获取漏洞列表失败: %v\n", err)
		return
	}

	// 处理响应
	fmt.Printf("总漏洞数: %d\n", resp.Data.Total)
	fmt.Printf("总页数: %d\n", resp.Data.PageTotal)
	fmt.Printf("当前页: %d\n", resp.Data.PageCurrent)
	fmt.Printf("每页大小: %d\n", resp.Data.PageSize)

	// 遍历漏洞列表
	for i, vul := range resp.Data.VulTraces {
		fmt.Printf("漏洞 #%d:\n", i+1)
		fmt.Printf("  ID: %s\n", vul.VulId)
		fmt.Printf("  名称: %s\n", vul.Name)
		fmt.Printf("  文件: %s\n", vul.Filename)
		fmt.Printf("  行号: %s\n", vul.RowNum)
		fmt.Printf("  风险等级: %s (ID: %d)\n", vul.Risk.NameLocale, vul.RiskId)
		fmt.Printf("  状态: %s (ID: %s)\n", vul.Tag.NameLocale, vul.TagId)
		fmt.Println()
	}
}
