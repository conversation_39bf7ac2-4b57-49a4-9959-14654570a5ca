





版权申明
版权所有 © 开源网安 2018。保留一切权利。
非经开源网安书面许可，任何单位和个人不得擅自摘抄、复制本文档的部分或全部内容，并不得以任何形式传播。

版本变更记录
序号	发布日期	作者	备注
1	2023/04/24	陆庆尚	首次新增
2	2023/05/11	陈培铭	接口（获取扫描结果）只返回漏洞分类树数据
3	2023/06/06	李便楠	修改sonar为代码质量
4	2023/06/20	胡兵	接口（创建git项目、创建svn项目、编辑git项目、编辑svn项目）增加增量扫描和快速扫描字段
5	2023/06/20	胡兵	查询扫描方案列表接口增加（isThird）字段
6	2023/06/26	刘财锋	接口（创建git项目、创建svn项目、编辑git项目、编辑svn项目）增加指定成员字段
7	2023/07/12	陈培铭	1、给接口分组归类
2、各接口增加小节“接口描述”
8	2023/07/27	胡兵	接口（创建git项目、创建svn项目、编辑git项目、编辑svn项目、编辑项目）增加（isLite）字段用于配置引擎扫描方式
9	2023/08/02	胡兵	1、新增获取报告导出配置列表接口
2、新增根据报告导出配置生成报告接口
10	2023/08/04	陈培铭	创建、编辑项目相关接口，入参（fileFilter），限制长度为：512
11	2023/08/21	陈培铭	创建、编辑项目相关接口，入参用户名（username），限制长度为：40
12	2023/08/30	贺实彬	编辑扫描方案接口调整入参字段与描述
13	2023/08/31	陈培铭	创建、编辑项目相关接口，入参：拉取指定文件/文件夹名称（pullFileName），限制长度为由原来100改成：512，也界面和数据库保持一致
14	2023/09/01	贺实彬	编辑扫描方案名称调整为非必填
15	2023/09/04	胡兵	1、获取左侧树和右侧列表接口增加入参（vulFlagType）用于查询缺陷跟踪；
2、扫描任务分类增加删除扫描记录接口
16	2023/09/05	胡兵	1、查询扫描进度接口增加字段：扫描开始时间（scanTime），扫描结束时间（finishTime），扫描时长（spendTime）
17	2023/09/05	胡兵	1、分页查询漏洞列表（漏洞列表页面右侧表格）接口增加返回参数（codingNodeId）用于查询漏洞详情
2、查询漏洞详情（漏洞列表页面点击右侧表格后）接口增加入参（type）用于区分查询类型
18	2023/10/13	胡兵	2、1、新增根据TFS信息创建项目接口
2、新增编辑TFS项目接口
3、编辑项目增加TFS方式
19	2023/10/30	胡兵	1、根据GIT、TFS信息创建项目接口（authenticationMethod）参数为非必填
20	2023/10/30	胡兵	1、创建空项目、根据GIT、SVN信息创建项目接口返回参数增加团队uuid
21	2023/10/31	胡兵	1、系统管理中的账号信息调整到外层用户管理
22	2023/10/31	胡兵	1、新增根据用户查询团队列表接口
23	2023/11/1	胡兵	1、新增根据团队UUID切换团队接口
24	2023/11/3	胡兵	1、编辑项目、编辑GIT、SVN项目接口返回参数增加团队uuid
25	2023/11/10	刘财锋	根据项目uuid生成报告接口增加是否生成审计信息参数，更新可以包含的内容模块
26	2023/11/13	贺实彬	1、获取漏洞分类接口增加返回参数（orderCode）分类顺序
27	2023/11/17	刘财锋	导出报告配置列表增加可选内容模块说明和审计信息说明
28	2023/11/28	刘财锋	3、1、增加部分限定访问接口的访问权限说明（4.2.6||*******-*******||*******-*******||4.5.3.1-*******||*******-*******||*******||*******-******* ||*******||*******-4.6.3.4）
29	2023/12/21	贺实彬	1、获取漏洞分类接口增加返回参数（orderCode）分类顺序
30	2024/04/09	贺实彬	1、生成报告接口增加报告类型
2、增加获取引擎列表信息接口
31	2024/04/16	刘财锋	获取扫描结果 （/project/{projectUuid}/task/{appId}/getScanResult）
下载报告 （/project/{projectUuid}/downloadReport）
根据导出配置生成报告 （/project/{projectUuid}/generateReportByConfig）
获取生成报告状态 （project/{projectUuid}/getGenerateReportStatus）
分页查询漏洞列表（漏洞列表页面右侧表格）（project/{projectUuid}/getListDetailByVulDataId）
获取扫描进度（/project/{projectUuid}/task/{appId}/getScanProgess）
标记漏洞状态
（/project/editVulTag）
以上接口增加指定权限校验团队uuid参数(permissionOrgUuid)
32	2024/05/09	蒋元林	根据GIT信息创建项目、根据SVN信息创建项目、根据TFS信息创建项目、编辑项目、编辑GIT项目、编辑SVN项目、编辑TFS项目以上接口新增输入参数voucherId(凭据认证ID)；
根据GIT信息创建项目、根据TFS信息创建项目、编辑项目、编辑GIT项目、编辑TFS项目新增authenticationMethod类型3：凭据认证；
新增查询凭证信息接口。
33	2024/05/17	胡兵	创建账号接口增加countryCode字段（国际区号）
34	2024/06/18	贺实彬	请求头增加语言
35	2024/07/10	胡兵	1. 获取扫描结果，返回参数调整：
(1) vulDataCnName调整成vulDataName
36	2024/07/10	胡兵	1. 分页查询漏洞列表（漏洞列表页面右侧表格），返回参数调整：
(1) data.vulTraces.cnName和data.vulTraces.enName调整成data.vulTraces.name（编码同步）
(2) data.vulTraces.tag.cnName和data.vulTraces.tag. enName调整成data.vulTraces.tag.nameLocale
(3) data.vulTraces.risk.cnName和data.vulTraces. risk. enName调整成data.vulTraces. risk.nameLocale
37	2024/07/10	胡兵	1. 查询漏洞详情（漏洞列表页面点击右侧表格后）返回参数调整：
(1) data.cnDesc调整成data.desc
(2) data.cnName调整成data.name
(3) data.cnRecommend调整成data. recommend
38	2024/07/10	胡兵	1. 获取漏洞分类列表详情（漏洞列表页面左侧树）返回参数调整：
(1)漏洞分组列表对象vulDataCnName调整成vulDataName
39	2024/07/10	胡兵	1. 根据语言查询安全规则漏洞树，返回参数调整：
(1) data.[i].riskCnName调整成data.[i].riskName
(2) data.[i].children.cnName和data.[i].children.enName调整成data.[i].children.name
40	2024/07/10	胡兵	1. 根据条件查询安全规则漏洞信息，返回参数调整：
(1) data.list[i].cnName调整成data.list[i].name
41	2024/07/10	胡兵	1. 根据漏洞ID查询漏洞信息，返回参数调整：
(1) data.cnName和data.enName调整成data.name
(2) data.cnDesc和data.enDesc调整成data.desc
(3) data.cnRecommend和data.enRecommend调整成data.recommend
42	2024/07/10	胡兵	1. 获取漏洞等级列表，返回参数调整：
(1) data.[i].cnName和data.[i].enName调整成data.[i].name
43	2024/07/10	胡兵	1. 获取编码规范类别列表，返回参数调整：
(1) data.[i].cnName和data.[i].enName调整成data.[i].name
44	2024/07/10	胡兵	1. 根据条件查询编码规范漏洞信息，返回参数调整：
(1) data.list[i].categoryCnName调整成data.list[i].categoryName
(2) data.list[i].cnName调整成data.list[i].name
(3) data.list[i].cnDesc调整成data.list[i].desc
(4) data.list[i].cnRecommendation调整成data.list[i]. recommendation
45	2024/07/10	胡兵	1. 分页查询代码质量缺陷列表，返回参数调整：
(1) data.list[i].cnName调整成data.list[i].name
46	2024/07/10	胡兵	1. 根据语言ID查询白名单对应支持漏洞信息，返回参数调整：
(1) data[i].cnName和data[i].enName调整成data[i].name
47	2024/07/23	刘财锋	告警管理增加入参邮件默认语言
48	2024/08/09	胡兵	创建白名单接口：增加effectiveType和projectUuids入参；
修改白名单接口：增加effectiveType和projectUuids入参；
查询白名单列表接口：增加effectiveType和projectUuids出参；
增加根据企业查询关联项目信息接口；
49	2024/08/16	胡兵	创建空项目、根据GIT信息创建项目、根据SVN信息创建项目、根据TFS信息创建项目、编辑项目、编辑GIT项目、编辑SVN项目、编辑TFS项目增加permissionType入参；
根据项目名称或项目id查询项目相关信息增加permissionType出参
50	2024/09/05	刘财锋	获取漏洞分类列表详情（漏洞列表页面左侧树）修改接口出参结构
51	2024/09/09	胡兵	增加对扫描记录备注操作的接口
52	2024/09/14	胡兵	获取漏洞分类列表详情（漏洞列表页面左侧树）增加signer入参
分页查询漏洞列表（漏洞列表页面右侧表格）增加signer入参
53	2024/10/24	蒋元林	增加扫描结果对比分析；
增加扫描结果对比分析-缺陷明细列表；
增加扫描结果对比分析-缺陷文件内容对比；
54	2024/10/24	蒋元林	分页查询漏洞列表和获取漏洞分类列表详情增加tagIdList入参；
55	2024/10/29	蒋元林	根据项目名称或项目id查询项目相关信息增加permissionOrgUuid入参
56	2024/10/31	胡兵	增加根据源码信息创建项目接口
57	2024/11/08	蒋元林	创建空项目、根据GIT信息创建项目、根据SVN信息创建项目、根据TFS信息创建项目、编辑项目、编辑GIT项目、编辑SVN项目、编辑TFS项目，更改orgUuid
58	2025/1/2	刘财锋	增加接口 漏洞统计
59	2025/1/15	蔡琦	修改查询扫描进度接口返回参数data.commitId备注
60	2025/3/5	蔡琦	新增 根据GIT信息创建项目并启动扫描、根据SVN信息创建项目并启动扫描 接口




目  录
1 概述 9
2 通用定义 9
2.1 API基本规则 9
2.2 请求头数据 9
3 签名机制 10
3.1 报文签名机制 10
4 接口定义 11
4.1 项目管理 11
4.1.1 加载开发语言列表 11
4.1.2 获取仓库依赖信息 12
4.1.3 创建空项目 13
4.1.4 上传源码文件 14
4.1.5 根据源码信息创建项目 15
4.1.6 根据GIT信息创建项目 17
4.1.7 根据SVN信息创建项目 19
4.1.8 根据TFS信息创建项目 21
4.1.9 编辑项目 23
4.1.10 编辑GIT项目 26
4.1.11 编辑SVN项目 29
4.1.12 编辑TFS项目 31
4.1.13 删除项目 33
4.1.14 根据项目名称或项目id查询项目相关信息 34
4.1.15 根据企业查询关联项目信息 35
4.2 扫描任务 36
4.2.1 发起扫描任务 36
4.2.2 查询扫描进度 37
4.2.3 终止扫描任务 38
4.2.4 重复发起扫描任务 39
4.2.5 获取扫描日志 39
4.2.6 删除扫描记录 40
4.2.7 扫描记录备注 41
4.3 检测结果 42
4.3.1 统计代码行数 42
4.3.2 获取扫描结果 43
4.3.3 分页查询漏洞列表（漏洞列表页面右侧表格） 45
4.3.4 查询漏洞详情（漏洞列表页面点击右侧表格后） 49
4.3.5 获取漏洞分类 50
4.3.6 获取漏洞分类列表详情（漏洞列表页面左侧树） 51
4.3.7 获取报告导出配置列表 53
4.3.8 根据报告导出配置生成报告 55
4.3.9 根据项目UUID生成报告 56
4.3.10 根据项目UUID查询报告生成情况 58
4.3.11 根据项目名称和报告ID下载报告 59
4.3.12 获取缺陷数据统计分类 60
4.3.13 获取缺陷数据统计环形图数据 61
4.3.14 获取缺陷数据统计折线图数据 62
4.3.15 查询项目缺陷汇总 63
4.3.16 扫描结果对比分析 65
4.3.17 扫描结果对比-缺陷明细列表 67
4.3.18 扫描结果对比-缺陷文件内容对比 69
4.4 缺陷审计 71
4.4.1 标记漏洞状态 71
4.5 规则管理 72
4.5.1 缺陷信息 72
4.5.2 编码规范 77
4.5.3 质量规则 81
4.5.4 检测规则 84
4.5.5 白名单管理 90
4.6 系统管理 97
4.6.1 系统信息 97
4.6.2 告警管理 100
4.6.3 凭证管理 106
4.7 用户管理 107
4.7.1 创建账号 107
4.7.2 删除账号 108
4.7.3 更新秘钥 109
4.7.4 查询当前用户团队列表 110
4.7.5 根据团队UUID切换团队 111






开源网安代码审核平台接口设计说明文档
1概述
CodeSec提供了丰富的REST API接口，客户可以通过对接这些接口与CodeSec进行集成。对接需要用到的账户信息，请联系具体的业务人员协助开通，真实的服务器地址请联系具体的技术负责人获取，当前文档中需要使用到服务器地址的地方，均以CS_URL表示。
2通用定义
2.1API基本规则
API请求必须同时包含基本路径以及API的版本号，构成规则如下：
http://{CS_URL}/{BASE_PATH}/{VERSION}/{METHOD}

本文档所涉及的API基本路径以及API版本信息如下：
基本路径：/cs/api
版本：v4
使用角色：团队管理员
则API的请求路径为：http://{CS_URL}/cs/api/v4/{METHOD}
2.2请求头数据
定义	类型	必选	描述
accessKey	string(2048)	Y	用户API访问KEY
x-cs-timestamp	string(64)	Y	时间戳，5分钟内数据有效
x-cs-nonce	string(64)	Y	随机字符串，防止重复提交
x-cs-signature	string(128)	Y	签名串
lang	string(10)	N	语言 简体中文 zh-CN 繁体中文 zh-TW English(US) en-US

3签名机制
为了保证数据传输过程中的真实性，完整性和不可抵赖，我们需要对数据进行数字签名，签名需要加入秘钥（accessSecret）（可通过界面,对接集成-Token设置，通过点击’’生成Token’获取accessSecret值），在接收签名数据之后进行数据签名校验。签名算法为sha256，结果为sha256字符串。签名的目的是为了防止客户端提交的请求以及通知数据被非法篡改。
3.1报文签名机制
根据接口报文内容，按照以下表格的规则生成待签名字符串，然后用SHA256签名算法对签名串进行签名，将签名结果串赋值到signature字段。
数据类型	生成规则	举例
PathValue	按照path中的顺序将所有的value进行拼接，末尾&客户端密钥&时间戳&随机字符串	url：http://CS_URL/openapi/user/998
signature：sha256(998&accessSecret&x-cs-timestamp&x-cs-nonce)
url：http://CS_URL/openapi/user/998/list/456
signature：sha256(998&456&accessSecret&x-cs-timestamp&x-cs-nonce)
Query	1.所有非空字段
2.按key的字典序排序
3.所有的key=value用“&”符号拼接
4.末尾&客户端密钥&时间戳&随机字符串	url：http://CS_URL/openapi/project?subId=100&type=static
signature：sha256 (subId=100&type=static&accessSecret&x-cs-timestamp&x-cs-nonce)

Form Data	1.所有非空字段
2.按key的字典序排序
3.所有的key=value用“&”符号拼接
4.末尾&客户端密钥&时间戳&随机字符串	参考Query的生成规则
Body	所有非空字段
按key的字典序排序
所有的key=value用“&”符号拼接
末尾&客户端密钥&时间戳&随机字符串	JSON举例：
{
    "subId":100,
    "type":"static",
    "projectData":{
        "id":11,
        "name":"test"
    }
}
signature：sha256 (subId=100&type=static &projectData=id=11&name=test&accessSecret&x-cs-timestamp&x-cs-nonce)

注意：
1.没有值的参数无需添加到签名串中，签名时将字符转换为字节流的时候统一使用UTF-8编码。
2.没有参数时请用 “ &客户端密钥&时间戳&随机字符 “ 进行加密 如sha256（&accessSecret&x-cs-timestamp&x-cs-nonce）。
3.签名串拼接顺序，先拼接Body，Form Data，Query再拼接PathValue最后&客户端密钥&时间戳&随机字符串。
4.签名代码可参考V2接口示例项目。
5.RSA算法所需公钥为：MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJkBK4+Mc6xRAWrSFL8NUoPwKZZ6aKRn/JbSlpYmvOIwva0MUvSVi3WBB/VGaFKWlz1ovv8SuWn8LFLVoSHFE8DDdyrFTuuRWS5hOtO2bvUgKpjiF+JUu1h92kNL04W8Sk7P2GGRk92FHksiY2rKP4teI3Uisu5oEyIXvPWHqmPwIDAQAB
6.文件上传方式可参考V2接口示例项目UploadSourcesExample类。
7.所有接口输入输出示例可参考V2接口示例项目ssp-api-examples。
4接口定义
4.1项目管理
4.1.1加载开发语言列表
*******接口描述
返回扫描类型对应的可用语言列表。
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/codeLanguage/list
*******输入参数
参数名	类型	必选	描述
type	int	N	扫描类型 默认1
(0 代码合规扫描，1 静态代码扫描)

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data[i].id	int	语言ID
data[i].name	string(256)	语言名称
data[i].suffixs	string(256)	语言文件名后缀


4.1.2获取仓库依赖信息
*******接口描述
返回配置好的java语言仓库和c#语言仓库。
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/getDependInfo
*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.javaDependInfo[i]	object	Java/Jsp依赖信息
data.javaDependInfo[i].id	Int	仓库依赖id
data.javaDependInfo[i].depotName	string(64)	仓库依赖名称
data.javaDependInfo[i].depotPath	string(256)	仓库依赖路径
data.javaDependInfo[i].depotType	Int	仓库依赖类型：1:本地仓库依赖
data.javaDependInfo[i].languageType	Int	语言类型：1: Java/Jsp  2: C#/ASP.NET
data.csharpDependInfo[i]	object	C#/ASP.NET依赖信息
data.csharpDependInfo[i].id	Int	仓库依赖id
data.csharpDependInfo[i].depotName	string(64)	仓库依赖名称
data.csharpDependInfo[i].depotPath	string(256)	仓库依赖路径
data.csharpDependInfo[i].depotType	Int	仓库依赖类型：1:本地仓库依赖
data.csharpDependInfo[i].languageType	Int	语言类型：1: Java/Jsp  2: C#/ASP.NET

4.1.3创建空项目
4.1.3.1接口描述
创建一个只有项目名的空项目，企业管理员调用此接口需要指定项目的归属团队或者指定成员将项目归属于共享团队。
4.1.3.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectByVoid
4.1.3.3输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称（长度2-512，项目名称不能包含￥()？！{}《》!#%&*/|:<>?\ 等特殊字符
）
projectDesc	string(500)	N	项目描述（500以下）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.orgUuid	string(40)	团队uuid


4.1.4上传源码文件
*******接口描述
为指定项目上传源码文件，每次上传会将之前的文件删除，只保留最后一次上传的文件。
*******接口定义
请求方式：post
请求头类型：Content-Type:multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/uploadSources
*******输入参数
参数名	类型	必选	描述
file	file	Y	源码文件，zip/tar.gz格式压缩包,大小不超过1G

注：文件上传需要使用 multipart/form-data 的方式上传代码和参数，请参考《附录：文件上传业务代码样例》
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.storeId	string	源码上传id
data.projectUuid	string(40)	项目uuid

4.1.5根据源码信息创建项目
*******接口描述
通过本地上传源码的方式创建项目，参考前端页面选择源代码来源：本地
*******请求定义
请求方式：post
请求头类型：Content-Type:multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectBySourceCode
*******输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称
projectDesc	string(500)	N	项目描述
file	file	Y	源码文件，zip/tar.gz格式压缩包,大小不超过1G
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
language	int	N	项目语言ID（不填时自动识别）
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见
isScanBinary	int	N	是否开启二进制扫描：0不开启（默认） 1开启
注意：当文件为jar或war时该参数需要开启才能扫描

specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户

注：文件上传需要使用 multipart/form-data 的方式上传代码和参数，请参考《附录：文件上传业务代码样例》

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid




4.1.6根据GIT信息创建项目
*******接口描述
根据git信息和项目信息创建一个git类型的项目。企业管理员调用此接口需要指定项目的归属团队或者指定成员将项目归属于共享团队。Token可以选择加密传输，默认不加密。
*******请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectByGitInfo
*******输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	Y	git地址
urlHead	int	N	git地址是否以https开头
0：否    1：是
gitType	int	Y	git类型
1 : gitlab
2 : github
3 : gitee
6 : gerrit
7 : bitbucket
authenticationMethod	int	N	git 认证类型
0.用户名密码认证（默认）
1.token认证
2.SSH密钥
3.凭据认证
username	string(40)	N	用户名（authenticationMethod=0时可用, authenticationMethod=1且gitType=7时可用）
password	string(512)	N	密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
token	string(128)	N	token（authenticationMethod=1时可用）
isTokenEncrypt	boolean	N	Token如果加密，需要传true，否则传false
sshKey	longtext	N	SSH密钥（authenticationMethod=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
branch	string(128)	N	分支名称 默认master
tag	string(128)	N	标签名称
checkCommitId	string(64)	N	指定拉取commitId
commitId	string(64)	N	提交Id
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID（不填时自动识别）
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
pullFileName	string(512)	N	拉取指定文件/文件夹名称
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见

specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户

4.1.6.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid

4.1.7根据GIT信息创建项目并启动扫描
4.1.7.1接口描述
根据git信息和项目信息创建一个git类型的项目并启动扫描。企业管理员调用此接口需要指定项目的归属团队或者指定成员将项目归属于共享团队。Token可以选择加密传输，默认不加密。
4.1.7.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectAndScanByGitInfo
4.1.7.3输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	Y	git地址
urlHead	int	N	git地址是否以https开头
0：否    1：是
gitType	int	Y	git类型
1 : gitlab
2 : github
3 : gitee
6 : gerrit
7 : bitbucket
authenticationMethod	int	N	git 认证类型
0：用户名密码认证（默认）
1：token认证
2：SSH密钥
3：凭据认证
username	string(40)	N	用户名（authenticationMethod=0时可用, authenticationMethod=1且gitType=7时可用）
password	string(512)	N	密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
token	string(128)	N	token（authenticationMethod=1时可用）
isTokenEncrypt	boolean	N	Token如果加密，需要传true，否则传false
sshKey	longtext	N	SSH密钥（authenticationMethod=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
branch	string(128)	N	分支名称 默认master
tag	string(128)	N	标签名称
checkCommitId	string(64)	N	指定拉取commitId
commitId	string(64)	N	提交Id
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID（不填时自动识别）
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
pullFileName	string(512)	N	拉取指定文件/文件夹名称
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见

specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户

4.1.7.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid
data.recordId	string(40)	扫描记录id

4.1.8根据SVN信息创建项目
4.1.8.1接口描述
根据svn信息和项目信息创建一个svn类型的项目，企业管理员调用此接口需要指定项目的归属团队或者指定成员将项目归属于共享团队。
4.1.8.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectBySvnInfo
4.1.8.3输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	Y	svn地址
username	string(40)	N	用户名
password	string(512)	N	密码（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
versionId	long	N	svn版本库ID
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见

specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户

4.1.8.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid

4.1.9根据SVN信息创建项目并启动扫描
4.1.9.1接口描述
根据svn信息和项目信息创建一个svn类型的项目并启动扫描，企业管理员调用此接口需要指定项目的归属团队或者指定成员将项目归属于共享团队。
4.1.9.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectAndScanBySvnInfo
4.1.9.3输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	Y	svn地址
username	string(40)	N	用户名
password	string(512)	N	密码（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
versionId	long	N	svn版本库ID
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见

specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户

4.1.9.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid
data.recordId	string(40)	扫描记录id

4.1.10根据TFS信息创建项目
4.1.10.1接口描述
根据tfs信息和项目信息创建一个tfs类型的项目。企业管理员调用此接口需要指定项目的归属团队。Token可以选择加密传输，默认不加密。
4.1.10.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/createProjectByTfsInfo
4.1.10.3输入参数
参数名	类型	必选	描述
projectName	string(512)	Y	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	Y	tfs地址
authenticationMethod	int	N	tfs认证类型
0：用户名密码认证（默认）
1：token认证
3：凭据认证
username	string(40)	N	用户名
password	string(512)	N	密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
token	string(128)	N	token（authenticationMethod=1时可用）
branch	string(128)	N	分支名称 默认main
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID（不填时自动识别）
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
ruleSetId	int	N	规则集ID（4.41接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必填（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认获取系统配置方式，无则默认深度引擎）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见


4.1.10.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id


4.1.11编辑项目
4.1.11.1接口描述
编辑项目相关信息，企业管理员调用此接口可以修改此项目的归属团队或者指定成员将项目归属于共享团队。Git类型的token可以选择加密传输，默认不加密。
4.1.11.2请求定义
请求方式：put
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/editProject
4.1.11.3输入参数
参数名	类型	必选	描述
sourceMode	int	Y	代码类型：
1：上传代码
2：git方式获取代码
3：svn方式获取代码
4：tfs方式获取代码
storeId	string(40)	N	源码上传id（sourceMode=1时必填）
versionId	long	N	svn版本库ID（sourceMode=3时可用）
projectName	string(512)	N	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	N	git/svn地址（sourceMode=2或3时必填）
urlHead	int	N	git地址是否以https开头（默认0）
0：否    1：是
gitType	int	N	git类型（sourceMode=2时必填）
1 : gitlab
2 : github
3 : gitee
6 : gerrit
authenticationMethod	int	N	git 认证类型（sourceMode=2时必填）
0.用户名密码认证
1.token认证
2.SSH密钥
3.凭据认证
username	string(40)	N	用户名（authenticationMethod=0且sourceMode=2时可用）
tfs支持（authenticationMethod=0和1使用）
password	string(512)	N	密码（authenticationMethod=0且sourceMode=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
token	string(128)	N	token（authenticationMethod=1且sourceMode=2时可用）
isTokenEncrypt	boolean	N	Token如果加密，需要传true，否则传false
（authenticationMethod=1且sourceMode=2时必填）
sshKey	longtext	N	SSH密钥（authenticationMethod=2且sourceMode=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
branch	string(128)	N	分支名称（authenticationMethod=0时可用）
tag	string(128)	N	标签名称（sourceMode=2时可用）
checkCommitId	string(64)	N	指定拉取commitId（sourceMode=2时可用）
commitId	string(64)	N	提交Id
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID （不填时自动识别语言）
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
pullFileName	string(512)	N	拉取指定文件/文件夹名称（如需设置空则传空字符串“”）
ruleSetId	int	N	规则集ID（”查询同组下全部规则集列表”接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见
specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户


4.1.11.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid

4.1.12编辑GIT项目
4.1.12.1接口描述
编辑git项目相关信息，企业管理员调用此接口可以修改此项目的归属团队或者指定成员将项目归属于共享团队。token可以选择加密传输，默认不加密。
4.1.12.2请求定义
请求方式：put
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/editGitProject
4.1.12.3输入参数
参数名	类型	必选	描述
projectName	string(512)	N	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	N	git地址
urlHead	Int	N	git地址是否以https开头
0：否    1：是
gitType	Int	Y	git类型
1 : gitlab
2 : github
3 : gitee
6 : gerrit
authenticationMethod	Int	Y	git 认证类型
0.用户名密码认证
1.token认证
2.SSH密钥
3.凭据认证
username	string(40)	N	用户名（authenticationMethod=0时可用）
password	string(512)	N	密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
token	string(128)	N	token（authenticationMethod=1时可用）
isTokenEncrypt	boolean	N	Token如果加密，需要传true，否则传false
sshKey	longtext	N	SSH密钥（authenticationMethod=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
branch	string(128)	N	分支名称
tag	string(128)	N	标签名称
checkCommitId	string(64)	N	指定拉取commitId
commitId	string(64)	N	提交Id
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	Int	N	项目语言ID
type	Int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	Int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
pullFileName	string(512)	N	拉取指定文件/文件夹名称（如需设置空则传空字符串“”）
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见
specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户


4.1.12.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid


4.1.13编辑SVN项目
4.1.13.1接口描述
编辑svn项目相关信息，企业管理员调用此接口可以修改此项目的归属团队或者指定成员将项目归属于共享团队。
4.1.13.2请求定义
请求方式：put
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/editSvnProject
4.1.13.3输入参数
参数名	类型	必选	描述
projectName	string(512)	N	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	N	svn地址
username	string(40)	N	用户名
password	string(512)	N	密码（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
versionId	Long	N	svn版本库ID
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	int	N	项目语言ID
type	int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
ruleSetId	int	N	规则集ID（4.44接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必须指定团队或者成员（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
specifiedMemberList	object	N	指定用户信息（用于企业管理员创建项目时指定用户）
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见

specifiedMemberList定义
参数名	类型	必选	描述
userId	string(40)	Y	用户uuid
roleId	int	Y	角色id 3:团队管理员 4:普通用户 8:只读用户


4.1.13.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id
data.orgUuid	string(40)	团队uuid

4.1.14编辑TFS项目
4.1.14.1接口描述
编辑tfs项目相关信息，企业管理员调用此接口可以修改此项目的归属团队。token可以选择加密传输，默认不加密。
4.1.14.2请求定义
请求方式：put
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/editTfsProject
4.1.14.3输入参数
参数名	类型	必选	描述
projectName	string(512)	N	项目名称
projectDesc	string(500)	N	项目描述
url	string(200)	N	tfs地址
authenticationMethod	Int	Y	tfs认证类型
0：用户名密码认证
1：token认证
3：凭据认证
username	string(40)	N	用户名（authenticationMethod=0时可用）
password	string(200)	N	密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
token	string(128)	N	token（authenticationMethod=1时可用）
branch	string(128)	N	分支名称
extraMark	string(512)	N	自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
fileFilter	string(512)	N	文件过滤或文件夹过滤 多个路径以","分隔
callBackUrl	string(100)	N	回调通知地址
language	Int	N	项目语言ID
type	Int	N	扫描类型 1 静态扫描（默认） 2 编码规范
isOpenDepend	Int	N	是否开启依赖：0不开启（默认） 1开启
depotId	int	N	仓库依赖id，开启依赖时填写
ruleSetId	int	N	规则集ID（4.41接口获取）
orgUuid	string(40)	N	指定团队Id 企业管理员调用该接口时必填（团管调用该接口时，可指定自己所属团队）
fastScan	int	N	是否开启快速扫描：0不开启（默认） 1开启
isIncrScan	int	N	是否开启增量扫描：0不开启（默认） 1开启
isLite	int	N	（支持Java语言）引擎扫描方式：0深度扫描引擎（默认获取系统配置方式，无则默认深度引擎）1：常规扫描引擎
voucherId	Int	N	凭证id，关联凭证id
permissionType	int	N	权限配置类型 0：团队可见 1：仅自己可见
团队管理员默认值为：0 团队可见
普通用户默认值为：1 仅自己可见


4.1.14.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.projectUuid	string(40)	项目Id
data.projectName	string(512)	项目名称
data.appId	string(40)	扫描任务Id


4.1.15删除项目
4.1.15.1接口描述
将项目放至回收站。
4.1.15.2请求定义
请求方式：delete
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/deleteProject
4.1.15.3输入参数
无
4.1.15.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在

4.1.16根据项目名称或项目id查询项目相关信息
4.1.16.1接口描述
根据项目名称或者项目id查询项目相关信息和所有的扫描任务的id，项目名称和项目id中至少携带一个，如果两个条件都传，则返回两个条件都符合的项目。
4.1.16.2请求定义
请求方式：GET
请求头类型：Content-Type: application/ form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/ {projectUuid}/getProjectOverView
4.1.16.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String(40)	N	项目Uuid
Query参数
参数名	类型	必选	描述
projectName	String(512)	N	项目名称
permissionOrgUuid
	String(40)	N	指定团队uuid（如果用户归属多个团队）

4.1.16.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据
data.projectUuid	String(40)	项目Uuid
data.projectName	String(512)	项目名
data.projectDesc	String(400)	项目描述
data.createTime	datetime	项目创建时间
data.userId	String(40)	项目创建人id
data.userName	String(40)	项目创建人
data.orgUuid	String(40)	项目所属团队
data.orgName	String(512)	项目所属团队名
data.appIdList	List<String(40)>	扫描任务id列表
data.statusTask	int	项目扫描状态:0：未完成 1：已完成
data.statusStaticCode	int	静态代码督察状态:0：未完成 1：已完成 2：扫描失败
data.statusDynamicCode	int	动态代码督察状态:0：未完成 1：已完成 2：扫描失败
data.permissionType	int	权限配置类型 0：团队可见 1：仅自己可见

4.1.17根据企业查询关联项目信息
4.1.17.1接口描述
根据企业ID分页查询团队下的所有关联项目信息
4.1.17.2请求定义
请求方式：GET
请求头类型：Content-Type: application/ form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/getProjectList
4.1.17.3输入参数
参数名	类型	必选	描述
fuzzyValue	String(128)	N	模糊搜索词（当前支持项目名称搜索）
pageCurrent	Int	N	当前页 默认1
pageSize	Int	N	一页查多少条 默认10

4.1.17.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据

【data定义】
data.total	Int	总共有多少条数据
data.pageTotal	Int	总共有多少页
data.pageCurrent	Int	当前页
data.pageSize	Int	一页查多少条
data.records	List	项目信息列表

【records定义】
定义	类型	描述
projectName	string(512)	项目名称
projectUuid	string(40)	项目uuid
orgName	string(128)	团队名称
orgUuid	string(40)	团队Uuid
userName	string(50)	创建人


4.2扫描任务
4.2.1发起扫描任务
4.2.1.1接口描述
发起指定项目的指定任务的扫描。
4.2.1.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid} /task/{appId}/scanSubProject

*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.appId	string(40)	扫描任务id
data.recordId	String(40)	扫描记录id

4.2.2 查询扫描进度
*******接口描述
返回指定任务的扫描进度，进度为0~100的整数。
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/getScanProgess

*******输入参数
Query参数
参数名	类型	必选	描述
permissionOrgUuid	String	N	权限团队uuid

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.progress	int	扫描进度
data.commitId	string(64)	最新提交的commitId（源代码来源选择GIT时返回）
data.scanTime	date	扫描开始时间
data.finishTime	date	扫描结束时间（扫描完成后返回）
data.spendTime	string	扫描时长（扫描完成后返回）


4.2.3 终止扫描任务
*******接口描述
终止指定任务的扫描。
*******请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/stopScanSubProject

*******输入参数
参数名	类型	必选	描述
recordId	string	N	当终止任务为重复扫描的任务，该字段必填
4.2.3.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据

4.2.4重复发起扫描任务
4.2.4.1接口描述
对某个正在进行扫描的任务重复发起扫描，如果任务没有正在扫描，会直接进行扫描，如果正在扫描，会在扫描完成后再次对此任务发起扫描。
4.2.4.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid} /task/{appId}/scanSubProjectRepeat

*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.appId	string(40)	扫描任务id
data.recordId	string(40)	扫描记录id

4.2.5获取扫描日志
*******接口描述
返回此次扫描的所有日志。
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/scanLog
*******输入参数
参数名	类型	必选	描述
recordId	string(40)	N	扫描记录Id
*******输出参数
参数名	类型	描述
status	boolean	状态
message	String (512)	描述信息
data	object
data.id	int	日志ID
data.message	text	日志信息
data.result	int	扫描是否完成：0：未完成，1：失败，2：已完成
data.appId	String(40)	扫描任务id
data.recordId	String(40)	扫描记录id

4.2.6删除扫描记录
*******接口描述
删除本次扫描记录和漏洞信息。此接口限定团队级和普通用户级用户访问。
*******请求定义
请求方式：delete
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/deleteScanRecord
*******输入参数
参数名	类型	必选	描述
recordId	string(40)	Y	扫描记录Id

*******输出参数
参数名	类型	描述
status	boolean	状态
message	String (512)	描述信息


4.2.7扫描记录备注
*******接口描述
扫描记录备注进行增加、修改、删除操作
*******请求定义
请求方式：Post
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/operateRemark
*******输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid
appId	String	Y	扫描任务Id

Query参数
参数名	类型	必选	描述
recordId	string(40)	Y	扫描记录Id
remark	string(128)	N	扫描备注信息

*******输出参数
参数名	类型	描述
status	boolean	状态
message	String (512)	描述信息



4.3检测结果
4.3.1统计代码行数
*******接口描述
返回统计代码中的文件数、可执行代码行数、代码注释行数、空白行数信息。
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/statisticCode

*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data[i].codeLanguageId	int	语言id
data[i].fileNum	int	文件数
data[i].codeLineNum	int	可执行代码行数
data[i].commentLines	int	代码注释行数
data[i].blankLines	int	空白行数


4.3.2获取扫描结果
*******接口描述
返回扫描结果的漏洞分类树。如果想获取漏洞分类树中某节点的漏洞列表，请调用接口（分页查询漏洞列表），然后根据列表某项的节点Id，调用接口（查询漏洞详情）获取漏洞详情信息。
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/getScanResult

*******输入参数
参数名	类型	必选	描述
recordId	string	N	扫描记录id（发起扫描时会返回）
permissionOrgUuid	string(40)	N	指定权限校验团队uuid，如果用户处于多个团队，可以增加此参数在不切换团队的情况下，访问自己属于的另一个团队的项目

*******输出参数
参数名	类型	描述
status	boolean	状态（程序错误为false，其余为true）
message	string	描述信息
data	object	返回数据
data.cyclomaticComplexityNum	int	圈复杂度超标个数
data.repetitiveLines	int	重复代码总行数
data.fileNum	int	文件数
data.fileSize	int	文件总大小(单位：kb)
data.language	object	项目语言
data.language.id	int	项目语言id
data.language.name	string(30)	项目语言名称
data.gitInfo	object	git信息
data.svnInfo	object	svn信息
data.scanResult	object	扫描结果
data.gitInfo.branch	string(128)	分支名称
data.gitInfo.commitId	string(64)	提交id
data.gitInfo.extraMark	string(512)	自定义标识符
data.svnInfo.versionId	long	svn版本号 不传或者传 -1 代表最新版本
data.svnInfo.extraMark	string(512)	自定义标识符
data.scanResult.securityVulNum	int	漏洞总数
data.scanResult.criticalNum	int	严重漏洞数
data.scanResult.highNum	int	高危漏洞数
data.scanResult.mediumNum	int	中危漏洞数
data.scanResult.lowNum	int	低危漏洞数
data.scanResult.noteNum	int	建议漏洞数
data.scanResult.newDiscoveryNum	int	新发现漏洞
data.scanResult.repeatNum	int	复发数
data.scanResult.isReviseNum	int	已修复数
data.scanResult.libraryNum	int	第三方库数量
data.scanResult.cveNum	int	Cve漏洞数
data.scanResult.cnnvdNum	int	Cnnvd漏洞数
data.scanResult.blankLines	Int	代码总空白行数
data.scanResult.commentLines	int	代码总注释行数
data.scanResult.codeLineNum	Int	可执行代码行数
data.scanResult.qualityWeaknessNum	int	质量规则漏洞数
data.scanResult.treeList[i]	list	漏洞节点
data.scanResult.treeList[i].lable	string(256)	树节点类型描述（例如 严重、高、中、低、建议 漏洞等级）
data.scanResult.treeList[i].children[i]	list	树子节点集合
data.scanResult.treeList[i].children[i].lable	string(256)	树节点类型描述（例如 "Cross-Site Request Forgery(6)"）
data.scanResult.treeList[i].children[i].vulDataName	string(256)	漏洞类型名称
data.scanResult.treeList[i].children[i].vulDataId	string	漏洞库ID集合 可能存在多个 以","分隔
data.scanResult.treeList[i].children[i].riskId	int	漏洞等级ID 1 严重 2高危 3中危 4 低危 5 建议 
data.scanResult.statisticalCodeList[i].codeLanguageName	string	语言类型
data.scanResult.statisticalCodeList[i].codeLineNum	int	代码行数
data.scanResult.statisticalCodeList[i].fileNum	int	文件数
data.scanResult.statisticalCodeList[i].codeRate	string	代码行数占比

4.3.3分页查询漏洞列表（漏洞列表页面右侧表格）
*******接口描述
返回按左侧漏洞树中漏洞库id（vulDataId）查询该漏洞下的所有数据，如果需要获取左侧漏洞树，请调用接口（获取漏洞分类列表详情（漏洞列表页面左侧树））
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/getListDetailByVulDataId
*******输入参数
参数名	类型	必选	描述
vulDataId	string(40)	Y	漏洞库id
recordId	string(40)	N	扫描记录id（发起扫描时会返回）
vulFlagType	int	N	缺陷跟踪类型：1：新发现 2：复发
type	int	N	查询类型：0：静态（默认）1：编码规范
pageCurrent	Int	N	当前页 默认1
pageSize	Int	N	一页查多少条 默认10
permissionOrgUuid	string(40)	N	指定权限校验团队uuid，如果用户处于多个团队，可以增加此参数在不切换团队的情况下，访问自己属于的另一个团队的项目
signer	string(40)	N	缺陷ID
安全缺陷查询：getListDetailByVulDataId接口返回的signer
编码规范查询：getListDetailByVulDataId接口返回的recordId
tagIdList	list	 N	缺陷状态id (默认值为1,2,3,4,5)
1 待确认 2已确认 3可疑 4 误报 5 已处理

4.3.3.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.total	Int	总共有多少条数据
data.pageTotal	Int	总共有多少页
data.pageCurrent	Int	当前页
data.pageSize	Int	一页查多少条
data.vulTraces	list	漏洞列表

漏洞列表对象
参数名	类型	描述
vulTrace	Object	漏洞信息
vulId	String(40)	漏洞列表 漏洞id
vulTypeId	String(40)	漏洞列表 漏洞类型id
filename	text	文件名
rowNum	String(10)	文件行数
vulDataId	String(40)	漏洞列表 漏洞库id
vulFlag	String(64)	漏洞列表 漏洞跟踪
name	String(256)	漏洞列表 漏洞名称
tagId	String(2)	漏洞列表 标签id
1 待确认 2已确认 3可疑 4 误报 5 已处理 
tag	object	漏洞标签信息
riskId	int	漏洞列表 漏洞风险等级
1 严重 2高危 3中危 4 低危 5 建议 
risk	object	风险等级描述
nodeList	list	漏洞节点列表
signer	String(40)	漏洞签名串（缺陷ID）

漏洞标签信息对象
参数名	类型	描述
id	int	标签ID
nameLocale	String(256)	标签名称

风险等级描述对象
参数名	类型	描述
id	int	风险等级ID
nameLocale	String(256)	风险等级中文名称
漏洞节点列表对象
参数名	类型	描述
id	int	漏洞节点Id
orgUuid	String(40)	团队id
appId	string(40)	扫描任务id
recordId	string(40)	扫描记录id
vulId	string(40)	漏洞id
ilename	text	文件名
lineCode	text	数据流节点的名称
lineNum	String(10)	所在行数
codeBlock	text	代码块
isSource	int	是否为源码：1：源代码；0：非源代码，可能是字节码

编码漏洞列表对象
参数名	类型	描述
codingNodeId	int(11)	获取编码规范数据时会返回（用于查询漏洞详情）
appId	String(40)	项目ID
scanTaskId	String(40)	扫描任务ID
recordId	String(40)	代码违规记录ID
fileName	text	文件名
rowBeginNum	int	开始行数
rowEndNum	int	结束行数
columnBeginNum	int	开始列数
columnEndNum	int	结束列数
confirmed	int	是否添加至漏洞管理：0未添加,1添加
ruleTypeId	int	分类ID
tagId	int	漏洞列表 标签id
1 待确认 2已确认 3可疑 4 误报 5 已处理 
tag	object	漏洞标签信息（参考漏洞标签信息对象）
riskId	int	漏洞列表 漏洞风险等级
1 严重 2高危 3中危 4 低危 5 建议 
risk	object	风险等级描述（参考风险等级描述对象）
typeName	string	类型名称
name	string	漏洞名称
languageName	string	语言



4.3.4查询漏洞详情（漏洞列表页面点击右侧表格后）
*******接口描述
返回按右侧漏洞列表中的漏洞节点id（vulNodeId）和漏洞库id（vulDataId）查询具体某一个漏洞数据。如果需要获取右侧漏洞列表数据，请调用接口（分页查询漏洞列表（漏洞列表页面右侧表格））
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/vulDetail

*******输入参数
参数名	类型	必选	描述
vulNodeId	int	Y	漏洞节点id（4.19 分页查询漏洞列表 接口中的data.vulTraces[i].nodeList[i].id）
vulDataId	string(40)	Y	漏洞库id
recordId	string(40)	N	扫描记录id
type	int	N	查询详情类型：0：静态（默认）1：编码规范

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.fileCont	object	文件内容
data.fileCont.cont	test	源码文本
data.fileCont.fileSuffix	string(20)	源码文件后缀
data.fileCont.totalRow	string(20)	源码总行数
data.fileCont.realRow	string(20)	定位到当前行
data.fileCont.startLine	string(20)	从data.fileCont.realRow开始读取几行
data.desc	text	漏洞描述
data.name	string(526)	漏洞名称
data.recommend	text	漏洞建议

4.3.5获取漏洞分类
*******接口描述
返回漏洞分类数据。如果想查询某个分类的漏洞信息，请调用接口（获取漏洞分类列表详情（漏洞列表页面左侧树））
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vul/getCategoryList

*******输入参数
参数名	类型	必选	描述
recordId	string(40)	N	扫描记录id（发起扫描时会返回）

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.categoryList[i]	list	漏洞分类列表
data.categoryList[i].id	int(11)	漏洞分组id
data.categoryList[i].name	string(128)	分类名称
data.categoryList[i].orderCode	string(128)	分类顺序
data.categoryList[i].status	int(11)	该分类是否启用 0：否   1：是

4.3.6获取漏洞分类列表详情（漏洞列表页面左侧树）
*******接口描述
返回漏洞列表左侧树，根据漏洞分类id查询该分类下的所有漏洞信息并组装成树形结构数据，如果需要获取所有漏洞分类，请调用接口（获取漏洞分类）
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/getVulList
注：该接口请求参数catId为6时，返回参数结构与其他情况不一致
*******输入参数
参数名	类型	必选	描述
catId	int	Y	漏洞分组id（获取漏洞分类接口获得）
recordId	string(40)	N	扫描记录id（发起扫描时会返回）
vulFlagType	int	N	缺陷跟踪类型：1：新发现 2：复发
signer	string(40)	N	缺陷ID
安全缺陷查询：getListDetailByVulDataId接口返回的signer
编码规范查询：getListDetailByVulDataId接口返回的recordId
tagIdList	list	 N	缺陷状态id (默认值为1,2,3,4,5)
1 待确认 2已确认 3可疑 4 误报 5 已处理
*******输出参数

参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	list	当返回成功的时候，data内容存在

data对象
参数名	类型	描述
languageId	int	语言id
languageName	String	语言名称
countNum	Int	总数
auditNum	Int	已审计数
nodeList	list	漏洞分类对象列表
漏洞分类对象
参数名	类型	描述
languageId	Int	语言id
id	int	当前分类id
typeName	String	分类名称
countNum	Int	总数
auditNum	Int	已审计数
childNodeList	List	漏洞分类对象列表（子分类，结构即当前对象）
vulDataCountList	List	漏洞信息列表（最底层分类才会有，如果数量为0则也没有）
漏洞信息列表
参数名	类型	描述
languageId	Int	语言id
vulDataId	string	第一层分类标签
typeId	int	所属分类id
riskId	String	严重等级
name	String	漏洞名称
countNum	Int	总数
auditNum	Int	已审计数

4.3.7获取报告导出配置列表
*******接口描述
获取报告管理中的报告导出配置列表
*******请求定义
请求方式：Get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/config/listAllExportConfig
*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.[i].id	Int	报告导出配置id
data.[i].name	string	报告导出配置名称
data.[i].reportFileType	Int	报告类型
data.[i].vulType	Int	缺陷分类
data.[i].riskList	list	漏洞等级
1.超危
2.高危
3.中危
4.低危
5. 建议
data.[i].sectionList	list	报告内容
2. 静态
3. 动态
data.[i].vulTagList	list	漏洞状态
1待确认
2已确认
3可疑
4误报
5 已处理
data.[i].encryptionEnable
	boolean	Xml是否加密:true加密，false不加密
data.[i].reportType	int	导出报告类型:1，专业版，2精简版
data.[i].codeCont	int	是否导出代码片段：0：不开启 1：开启
data.[i].contrastCont	int	是否导出对比结果:0不开启，1开启
data.[i]. auditInfo	Int	是否包含审计信息0:不显示（默认）1:显示
data.[i].reportId	int	自定义模板配置
data.[i].codePartLimit	int	代码片段行数
data.[i].contentModule	string	内容模块：1：漏洞概览；2：漏洞明细；3：漏洞建议4:第三方组件缺陷; 5:工具信息及参考标准（可组合）

4.3.8根据报告导出配置生成报告
4.3.8.1接口描述
根据报告导出配置生成对应报告。
4.3.8.2请求定义
请求方式：Post
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/generateReportByConfig
4.3.8.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid

Query参数
参数名	类型	必选	描述
exportConfigId	int	Y	报告导出配置id（通过获取报告导出配置列表接口获取）
recordId	string	N	扫描记录id，为空时生成最近一次扫描成功的报告
permissionOrgUuid	string(40)	N	指定权限校验团队uuid，如果用户处于多个团队，可以增加此参数在不切换团队的情况下，访问自己属于的另一个团队的项目

4.3.8.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

4.3.9根据项目UUID生成报告
4.3.9.1接口描述
根据项目UUID生成最近一次扫描成功的报告。
当报告类型选择excel和json时，仅支持选择漏洞等级和漏洞状态。
4.3.9.2请求定义
请求方式：Post
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/ {projectUuid}/generateReport
4.3.9.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid

Query参数
参数名	类型	必选	描述
reportFileType	int	N	报告类型
1．word
2．pdf（默认）
3．xml
4．excel
5．html
6．json
vulCategory	int	N	漏洞类型 可以导出的分类需要根据项目语言决定，可以参考获取漏洞分类接口
1,OWASP Top 10 2013
2,PCI 3.2.1
3,CWE/SANS Top 25 2011
4,安全等级
5,CodeSec 规则分类
6,GB/T 39412-2020 信息安全技术—代码安全审计规范
7,MISRA C 2012
8,OWASP Top 10 2021
9,CWE
10,GB/T 34944-2017 Java语言源代码缺陷测试规范
11,GB/T 34946-2017 C#语言源代码缺陷测试规范
12,GB/T 34943-2017 C/C++语言源代码缺陷测试规范
15,GJB 8114-2013 C/C++语言编程安全子集
16,Cert Java
19,GJB 5369-2005 航天型号软件C语言安全子集
20,GB/T 38674-2020 Java信息安全技术 应用软件安全编程指南
21,GB/T 38674-2020 C/C++信息安全技术 应用软件安全编程指南
22,Cert C
23,代码质量
24,QGDW10929.5-2018
25,MISRA C 2004
26,ISO/IEC 5055:2021
riskIdList	list	N	漏洞等级，默认[1,2,3,4,5]
sectionIdList	list	N	报告内容
2. 静态（默认）
3. 动态
vulTagIdList	list	N	漏洞状态，默认[1,2,3,4]
5.待确认
6.已确认
7.可疑
误报
isEncryption	boolean	N	【reportFileType为3时（xml），当前参数才生效】xml中的描述及修复建议是否加密：
true加密（默认）
false不加密
reportType	int	N	是否显示节点信息
0:不显示
1:显示
codeCont	int	N	是否显示代码片段
0:不显示（默认）
1:显示
contrastCont	int	N	是否显示与上一次扫描结果对比
0:不显示（默认）
1:显示
auditInfo	int	N	是否包含审计信息
0:不显示（默认）
1:显示
contentModule	String	N	内容模块：1：漏洞概览；2：漏洞明细；3：漏洞建议4:第三方组件缺陷; 5:工具信息及参考标准（可组合）
reportId	int	N	报告模板：1：系统模板;

4.3.9.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据

4.3.10根据项目UUID查询报告生成情况
4.3.10.1接口描述
返回调用（根据项目UUID生成报告）接口后的生成情况数据，数据包含（正在生成、生成成功、生成失败、已取消生成）。
4.3.10.2请求定义
请求方式：Get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/ {projectUuid}/getGenerateReportStatus
4.3.10.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid
Query参数
参数名	类型	必选	描述
permissionOrgUuid	string(40)	N	指定权限校验团队uuid，如果用户处于多个团队，可以增加此参数在不切换团队的情况下，访问自己属于的另一个团队的项目

4.3.10.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	Object	返回数据
data.message	string	报告生成情况
正在生成
生成成功
生成失败
已取消生成
data.reportRecordId	int	报告ID



4.3.11根据项目名称和报告ID下载报告
4.3.11.1接口描述
调用（根据项目UUID查询报告生成情况）接口，当message为生成成功时，则下载报告。
4.3.11.2请求定义
请求方式：Get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/ {projectUuid}/downloadReport
4.3.11.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid

Query参数
参数名	类型	必选	描述
reportRecordId	int	Y	报告ID
permissionOrgUuid	string(40)	N	指定权限校验团队uuid，如果用户处于多个团队，可以增加此参数在不切换团队的情况下，访问自己属于的另一个团队的项目

4.3.11.4输出参数
无
4.3.12获取缺陷数据统计分类
4.3.12.1接口描述
返回缺陷数据统计分类，根据记录源码语言查询支持的图形统计分类数据。语言不一样返回的分类数据也会变化。
4.3.12.2请求定义
请求方式：get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/getCountVulCategory

4.3.12.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid

Query参数
参数名	类型	必选	描述
recordId	String	Y	扫描记录id

4.3.12.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data[i].id	string(40)	分类id
data[i].name	string(40)	分类名称
data[i].orderCode	string(40)	排序列
data[i].status	integer(11)	状态：0：未启用, 1：启用
data[i].subCats	object	子分类






4.3.13获取缺陷数据统计环形图数据
4.3.13.1接口描述
返回缺陷数据统计环形图数据，如果需要某个分类下的数据，请调用接口（获取缺陷数据统计分类），然后即可按分类id查询。
4.3.13.2请求定义
请求方式：get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/listWeaknessInfo
4.3.13.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid

Query参数
参数名	类型	必选	描述
recordId	String	Y	扫描记录id
appId	String	Y	扫描任务id
catId	Int	Y	分类id

4.3.13.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.typeName	String	类型名称
data.typeNum	Int	类型漏洞数量


4.3.14获取缺陷数据统计折线图数据
4.3.14.1接口描述
返回缺陷数据统计折线图数据，如果需要某个分类下的数据，请调用接口（获取缺陷数据统计分类），然后即可按分类id查询。
4.3.14.2请求定义
请求方式：get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/listWeaknessInfoTrend
4.3.14.3输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid

Query参数
参数名	类型	必选	描述
recordId	String	Y	扫描记录id
appId	String	Y	扫描任务id
catId	Int	Y	分类id
startTime	String	N	开始时间
overTime	String	N	结束时间

4.3.14.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.timeList	list	扫描完成时间数组
data.trendList	list	数据集合
data.trendList[i].typeName	String	类型名称
data.trendList[i].valueList	list	每次扫描的类型数据


4.3.15查询项目缺陷汇总
********接口描述
返回安全漏洞数量、编码规范漏洞数量、代码质量缺陷数量汇总信息。
********请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/getSummaryData
********输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid
Query参数
参数名	类型	必选	描述
appId	string	Y	扫描任务id
recordId	string	Y	扫描记录id
********输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.codeSum	int	项目代码行数
data.finishTime	int	扫描完成时间
data.spendTime	int	扫描时长
data.seriousList[i].typeName	int	安全漏洞类型
data.seriousList[i].typeNum	List	安全漏洞数量
data.codingList[i].typeName	string(40)	编码规范漏洞类型
data.codingList[i].typeNum	string(526)	编码规范漏洞数量
data.sonarList[i].typeName	string(30)	代码质量缺陷类型
data.sonarList[i].typeNum	int	代码质量缺陷数量

4.3.16扫描结果对比分析
********接口描述
该接口返回的是两个扫描记录的对比结果，获取扫描开始\结束时间，缺陷总数，代码行数，扫码时长，检测对象，扫描方案，扫描类型，缺陷跟踪的对比分析。
********请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/compareResult

********输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid
appId	String	Y	扫描任务Id

Query参数
参数名	类型	必选	描述
lastRecordId
	string(40)	Y	先前扫描
recordId	string(40)	Y	新增扫描
********输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.newScanRecordInfo
	object	新扫描记录
data.oldScanRecordInfo
	object	旧扫描记录

data.(old/new)ScanRecordInfo.scanTime	date
	扫描时间
data.(old/new)ScanRecordInfo.finishTime	date	扫描完成时间
data.(old/new)ScanRecordInfo.codeTotalLIneNum	int	代码总行数
data.(old/new)ScanRecordInfo.projectName	string	项目名称
data.(old/new)ScanRecordInfo.vulTotalNum	int	漏洞数量
data.(old/new)ScanRecordInfo.languageNameList	string	检测语言对象，list字符串
data.(old/new)ScanRecordInfo.presetName	string	扫描方案
data.(old/new)ScanRecordInfo.finishTimeStatistics	string	扫描完成时间统计
data.(old/new)ScanRecordInfo.isIncrScan	int	扫描类型0-全量扫描 1-增量扫描
data.vulContrastData	object	缺陷对比数据

data.vulContrastData.newCriticalVul	int	新发现-超危
data.vulContrastData.newHighVul	int	新发现-高危
data.vulContrastData.newMediumVul	int	新发现-中危
data.vulContrastData.newLowVul	int	新发现-低危
data.vulContrastData.newNoteVul
	int	新发现-建议
data.vulContrastData.oldCriticalVul	int	复发-超危
data.vulContrastData.oldHighVul	int	复发-高危
data.vulContrastData.oldMediumVul	int	复发-中危
data.vulContrastData.oldLowVul	int	复发-低危
data.vulContrastData.oldNoteVul
	int	复发-建议
data.vulContrastData.resolvedCriticalVul	int	已解决-超危
data.vulContrastData.resolvedHighVul	int	已解决-高危
data.vulContrastData.resolvedMediumVul	int	已解决-中危
data.vulContrastData.resolvedLowVul	int	已解决-低危
data.vulContrastData.resolvedNoteVul
	int	已解决-建议
data.vulContrastData.oldComCriticalVul	int	先前扫描-超危漏洞
data.vulContrastData.oldComHighVul	int	先前扫描-高危漏洞
data.vulContrastData.oldComMediumVul	int	先前扫描-中危漏洞
data.vulContrastData.oldComLowVul	int	先前扫描-低危漏洞
data.vulContrastData.oldComNoteVul
	int	先前扫描-建议漏洞
data.vulContrastData.newComCriticalVul	int	新增扫描-超危漏洞
data.vulContrastData.newComHighVul	int	新增扫描-高危漏洞
data.vulContrastData.newComMediumVul	int	新增扫描-中危漏洞
data.vulContrastData.newComLowVul	int	新增扫描-低危漏洞
data.vulContrastData.newComNoteVul	int	新增扫描-建议漏洞

4.3.17扫描结果对比-缺陷明细列表
********接口描述
该接口返回的是两个扫描记录的对比结果缺陷列表，列表内容有：获取缺陷名称，缺陷跟踪状态，语言ID，严重等级，缺陷状态，入口节点，入口行数，文件路径。
********请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/compareVulDetailInfo


********输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid
appId	String	Y	扫描任务Id

Query参数
参数名	类型	必选	描述
lastRecordId
	string(40)	Y	先前扫描
recordId	string(40)	Y	新增扫描
vulDataName
	string(128)	N	缺陷名称

vulFlag
	string(1)	N	缺陷跟踪状态-1:新发现，2:复发,3:已解决

languageId
	int	N	语言ID；可根据扫描结果对比分析中的data.(old/new)ScanRecordInfo.languageNameList确认范围
riskId	int	N	严重等级-1:超危 2:高危 3:中危 4:低危 5:建议

vulTagList
	list	N	缺陷状态集合-1:待确认，2:已确认，3:可疑，4:误报，5:已处理

pageCurrent	int	N	当前页 默认1
pageSize	int	N	一页查多少条 默认10
********输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.vulTraces[i]
	object	缺陷队列
data.vulTraces[i].recordId
	string	扫描记录ID
data.vulTraces[i].vulId
	string
	缺陷ID
data.vulTraces[i].filename
	string	扫描文件
data.vulTraces[i].name
	string	缺陷名称
data.vulTraces[i].riskId
	int	缺陷严重等级（1:超危 2:高危 3:中危 4:低危 5:建议
）
data.vulTraces[i].tagId
	int	缺陷状态（1:待确认，2:已确认，3:可疑，4:误报，5:已处理）
data.vulTraces[i].vulFlag
	int	缺陷跟踪（1:新发现，2:复发,3:已解决）

data.vulTraces[i].nodeLis[i]
	object	缺陷数据流图
data.vulTraces[i].nodeLis[i].lastLineNum
	string	入口行数（nodeList[max]就是入口）
data.vulTraces[i].nodeLis[i].lastLineCode
	string	入口节点（nodeList[max]就是入口）
data.vulTraces[i].nodeLis[i].id
	int	漏洞节点ID
data.vulTraces[i].nodeLis[i].filename	string	文件路径
data.vulTraces[i].nodeLis[i].realFileName	string	真实路径

pageCurrent	int	当前页
pageSize	int	当前页大小
pageTotal	int	总页数
recordTotal	int	总记录数

4.3.18扫描结果对比-缺陷文件内容对比
********接口描述
返回两个扫描记录的对比结果，获取缺陷列表中的文件内容。
********请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/getFileContent



********输入参数
Pathvalue参数
参数名	类型	必选	描述
projectUuid	String	Y	项目Uuid
appId	String	Y	扫描任务Id

Query参数
参数名	类型	必选	描述
lastRecordId
	string(40)	Y	先前扫描
recordId	string(40)	Y	新增扫描
filePath

	string(500)	Y	文件路径；对应扫描结果对比-缺陷明细列表中的真实路径
vulNodeId

	int	Y	漏洞节点ID；对应扫描结果对比-缺陷明细列表中的漏洞节点ID（data.vulTraces[i].nodeLis[i].id）
vulName
	string	N	缺陷名称
nodePosition
	int	N	缺陷下标 0第一个 中间1 最后一个2


rowNum
	int	N	缺陷行数

********输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.fileCont[i]
	object	新增扫描文件内容
data.lastFileCont[i]
	object	先前扫描文件内容
data.lastfileCont[i].cont
	string
	文件内容
data.fileCont[i].fileSuffix
	string	文件后缀名
data.fileCont[i].realRow
	int	缺陷标记行数
data.fileCont[i].startLine
	int	文件起始行数
data.fileCont[i].endLine
	int	文件结尾行数
data.fileCont[i].totalRow
	int	文件总行数

4.3.19漏洞统计
********接口描述
根据筛选条件，返回漏洞多个统计维度
********请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/{projectUuid}/task/{appId}/getVulCount
********输入参数
参数名	类型	必选	描述
recordId	string(40)	Y	扫描记录id（发起扫描时会返回）
catId	Int	N	一级分类id 默认严重等级分类
subCatId	Int	N	多级分类id 默认不指定（左侧树接口返回结果中的漏洞分类对象的id，本接口返回结果中的缺陷分类数量统计对象的id；多级分类和一级分类不一致的情况下，主要采用多级分类；安全等级分类下的分类id不能使用，如果安全等级需要指定严重等级，需要使用riskIdList参数）
vulFlagType	int	N	缺陷跟踪类型：1：新发现 2：复发 默认不指定
tagIdList	List[int]	N	缺陷状态id (默认值为1,2,3,4,5)
1 待确认 2已确认 3可疑 4 误报 5 已处理
riskIdList	List[int]	N	风险等级id (默认值为1,2,3,4,5)
1 超危 2高危 3中危 4 低危 5 建议
permissionOrgUuid	string(40)	N	指定权限校验团队uuid，如果用户处于多个团队，可以增加此参数在不切换团队的情况下，访问自己属于的另一个团队的项目

4.3.19.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.riskCountList	list	风险等级数量统计
data.vulFlagCountList	list	缺陷跟踪状态数量统计
data.tagCountList	list	缺陷状态数量统计
data.typeCountList	list	缺陷分类数量统计
data.vulDataCountList	list	缺陷漏洞数量统计

风险等级数量统计对象
参数名	类型	描述
id	int	风险等级ID
nameLocale	String(256)	风险等级名称
count	int	风险等级数量

缺陷跟踪数量统计对象
参数名	类型	描述
id	int	缺陷跟踪ID
nameLocale	String(256)	缺陷跟踪名称
count	int	缺陷跟踪数量

缺陷状态数量统计对象
参数名	类型	描述
id	int	缺陷状态ID
nameLocale	String(256)	缺陷状态名称
count	int	缺陷状态数量

缺陷分类数量统计对象
参数名	类型	描述
id	int	缺陷分类ID
nameLocale	String(256)	缺陷分类名称
count	int	缺陷状态数量
childNodeList	List	子分类数量统计对象列表（对象即为当前对象）

缺陷漏洞数量统计
参数名	类型	描述
vuldataId	string	缺陷漏洞ID
nameLocale	String(256)	缺陷漏洞名称
count	int	缺陷漏洞数量


4.4缺陷审计
4.4.1标记漏洞状态
4.4.1.1接口描述
任务扫描完成后默认漏洞状态为待确认，可修改某个漏洞的状态，状态包含（1．待确认、2．已确认、3．可疑、4. 误报、5. 已处理）。
4.4.1.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/editVulTag

4.4.1.3输入参数
参数名	类型	必选	描述
tagId	int	Y	漏洞状态ID
1．待确认
2．已确认
3．可疑
4. 误报
5. 已处理
vulIds	string	Y	漏洞vulId集合，举例：vulIds: “xxx1,xxx2,xx3”
多个vulId以英文逗号隔开
recordId	string	Y	漏洞集合对应的扫描记录的id，添加后可提高查询速度，建议添加。
permissionOrgUuid	String	N	权限团队uuid

4.4.1.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.vulIds	object	漏洞信息
data.vulIds.vulId	string(40)	漏洞id
data.vulIds.projectUuid	string(40)	项目id
data.vulIds.projectName	string(512)	项目名称

4.5规则管理
4.5.1缺陷信息
*******根据语言查询安全规则漏洞树
*******.1接口描述
返回每个语言的漏洞列表。语言ID可通过调用加载开发语言列表获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vul/treeListByLanguage
*******.3输入参数

参数名	类型	必选	描述
languageId	Int 	Y	语言ID（4.4获取语言ID）
name	string(526)	N	漏洞名称
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据
data.[i].riskName	string(10)	漏洞级别名称
data. [i].riskId	string(11)	漏洞级别ID
data. [i].children	List	子节点
data.[i].children.vulDataId	string(40)	漏洞ID
data.[i].children.name	string(526)	漏洞名称


*******根据多个语言查询安全规则漏洞树
*******.1接口描述
根据多个语言查询安全规则漏洞树，返回数据为Map对象。键为语言名称。值为对应漏洞列表。语言ID可通过调用加载开发语言列表获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vul/treeListByLanguageIds
*******.3输入参数

参数名	类型	必选	描述
languageIds	string		Y	语言ID（4.4获取语言ID）
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据，是一个map对象，key是语言名称
data[key][i].riskName	string(10)	漏洞级别名称
data[key][i].riskId	string(11)	漏洞级别ID
data[key][i].children	List	子节点
data[key][i].children.vulDataId	string(40)	漏洞ID
data[key][i].children.name	string(526)	漏洞名称


*******根据条件查询安全规则漏洞信息
*******.1接口描述
分页返回安全规则漏洞信息列表。调用加载开发语言列表获取语言ID。调用获取漏洞等级列表获取漏洞等级ID。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vul/findVulDataListByPage
*******.3输入参数

参数名	类型	必选	描述
languageId	Int 	N	语言id（4.4获取语言ID）
name	String(526)	N	漏洞名称
riskId	Int	N	漏洞级别ID
pageSize	int	Y	每页个数
pageCurrent	int	Y	当前页数
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	漏洞信息集合
data.list[i].vulDataId	string(40)	漏洞ID
data.list[i].name	string(526)	漏洞名称
data.list[i].languageId
	int	语言ID
data.list[i].languageName
	string(30)	语言名称
data.list[i].riskId	int	漏洞级别ID
data.list[i].enable	Int	漏洞是否可用 0可不用 1可用
data.list[i].securityStandards	string	关联关系

*******根据漏洞ID查询漏洞信息
*******.1接口描述
返回单个漏洞详细信息。漏洞Id可通过调用根据条件查询安全规则漏洞信息或者根据多个语言查询安全规则漏洞树或者根据语言查询安全规则漏洞树获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vul/getVulData
*******.3输入参数

参数名	类型	必选	描述
vulDataId	string(40)	Y	漏洞id
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.id	int	id
data.vulDataId	String(40)	漏洞ID
data.riskId	Int	漏洞级别ID
data.languageId	int	语言ID
data.languageName
	String(30)	语言名称
data.name	String(526)	漏洞名称
data.enable	int	是否可用 0不可用1可用
data.desc	text	漏洞描述
data.recommend	text	漏洞修复建议
data.isEdit	int	是否可编辑 0不可用1可用

*******获取漏洞等级列表
*******.1接口描述
返回漏洞安全等级列表。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/risk/riskList
*******.3输入参数
无

*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据
data.[i].id	int	漏洞级别id 对应riskId
data.[i].name	String(10)	漏洞名称
data.[i].status	int	漏洞级别状态0不可用1可用

4.5.2编码规范
*******获取编码规范类别列表
*******.1接口描述
返回指定语言的编码规范类别列表。语言ID可通过调用加载开发语言列表获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/codingRules/listType
*******.3输入参数

参数名	类型	必选	描述
languageId	int	N	语言id，默认1

*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据
data.[i].name	String(10)	类别名称
data.[i].desc	text	类别描述
data.[i].id	int	分类id


*******根据条件查询编码规范漏洞信息
*******.1接口描述
分页返回编码规范漏洞信息列表。调用加载开发语言列表获取语言ID。调用获取漏洞等级列表获取漏洞等级ID。调用获取编码规范类别列表获取类别ID。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/codingRules/listByPage
*******.3输入参数

参数名	类型	必选	描述
languageId	Int 	N	语言id 默认1
name	String(526)	N	漏洞名称
riskId	Int	N	风险等级id
categoryId	int	N	类别id
pageSize	int	Y	每页个数
pageCurrent	int	Y	当前页数
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	漏洞信息集合
data.list[i].categoryName	string(40)	漏洞分类名称
data.list[i].categoryId	int	漏洞分类id
data.list[i].languageId
	int	语言ID
data.list[i].languageName
	string(30)	语言名称
data.list[i].risk	int	风险等级id
data.list[i].name	Int	漏洞名称
data.list[i].desc	string	描述
data.list[i].recommendation		修复建议
data.list[i].id		漏洞id
data.list[i].status	int	状态 0冻结 1未冻结

*******根据多个语言查询编码规范漏洞树
*******.1接口描述
根据多个语言查询编码规范漏洞树，返回数据为Map对象。键为语言名称。值为对应漏洞列表。语言ID可通过调用加载开发语言列表获取。该接口适用于懒加载树，每次仅返回一层数据。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/codingRules/treeListByLanguageIds
注：该接口仅返回一层数据，parentId传值和不传值，返回的数据结构不一致
*******.3输入参数
参数名	类型	必选	描述
languageIds	string		N	语言id 默认1
parentId	int	N	父级id 默认0
ruleSetId	int	N	扫描方案id 默认0
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据，是一个map对象，key是语言名称
当parentId为空时，返回的数据结构
参数名	类型	描述
data	object	返回数据，是一个map对象，key是语言名称
key	string(512)	语言名称，data的key
data[key][i].change	int	0删除1增加
data[key][i].id	int	漏洞id
data[key][i].parentId	int	父级id
data[key][i].status	int	0未勾选 1半选2勾选
data[key][i].type	int	0种类 1规则
当parentId不为空时，返回的数据结构
参数名	类型	描述
data	object	返回数据，对应分类下的漏洞集合
data[i].change	int	0删除1增加
data[i].id	int	漏洞id
data[i].parentId	int	父级id
data[i].status	int	0未勾选 1半选2勾选
data[i].type	int	0种类 1规则

4.5.3质量规则
4.5.3.1查询代码质量缺陷是否开启
4.5.3.1.1接口描述
查询是否启用代码质量扫描。此接口限定企管级用户访问。
4.5.3.1.2请求定义
请求方式：get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/sonar/getEnable
4.5.3.1.3输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.status	Boolean	是否开启

4.5.3.2查询代码质量缺陷查询请求参数
4.5.3.2.1接口描述
返回代码质量缺陷数据查询所需要的请求参数。此接口限定企管级用户访问。
4.5.3.2.2请求定义
请求方式：get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/sonar/getSelectParams
4.5.3.2.3输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.languageList[i].languageName	string	语言名称
data.languageList[i].codeLanguageId	int	语言id
data.severityList[i].sonarRuleSeverity	string	严重等级名称
data.severityList[i].riskId	int	严重等级id
data.typeList[i].sonarRuleType	string	类型名称
data.typeList[i].type	string	类型名称（查询时使用该字段）

*******分页查询代码质量缺陷列表
*******.1接口描述
返回代码质量缺陷数据分页列表。通过调用查询代码质量缺陷查询请求参数获取请求参数。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/sonar/listByPage
*******.3输入参数

参数名	类型	必选	描述
languageId	Int 	N	语言id（4.4获取语言ID）
name	String(526)	N	漏洞名称
type	string	N	漏洞类型
riskId	Int	N	漏洞级别ID
pageSize	int	Y	每页个数
pageCurrent	int	Y	当前页数
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	漏洞信息集合
data.list[i].vulDataId	string(40)	漏洞id
data.list[i].name	string(526)	漏洞名称
data.list[i].langName
	string(30)	语言名称
data.list[i].riskId	int	风险等级id
data.list[i].thirdType	string	漏洞类型

4.5.4检测规则
*******创建扫描方案信息
*******.1接口描述
根据语言Id和漏洞Id创建扫描方案。语言ID可通过调用加载开发语言列表获取，漏洞ID可通过调用根据多个语言查询安全规则漏洞树获取，编码规范漏洞ID可通过调用根据多个语言查询编码规范漏洞树获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：post
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vulrule/createRuleSet

*******.3输入参数

参数名	类型	必选	描述
name	string(20)	Y	规则集名称
languageIds	Int	Y	语言ID（4.4获取语言ID）
vulDataIds	String	Y	漏洞id集合字符串(4.27获取漏洞ID) 逗号拼接
status	Int	Y	0为不生效，1为生效
isDefault	int	N	默认扫描方案 0否1是 默认0
desc	string(240)	N	扫描方案描述
codingIdList	string	N	编码规范漏洞id，多个使用逗号拼接，该字段取值为4.33返回结果中的id-type-change,使用-拼接
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.id	Int	规则集ID

*******查询扫描方案列表
*******.1接口描述
根据条件分页返回扫描规则集列表。语言ID可通过调用加载开发语言列表获取。此接口限定企管级用户访问。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vulrule/rulelist
*******.3输入参数

参数名	类型	必选	描述
languageId	Int 	N	语言id（4.4获取语言ID）
name	string(20)	N	规则集名称
isThird	Int	N	查询非内置与所有规则：0查询非内置规则（默认） 1 查询所有规则
pageSize	int	N	每页个数
pageCurrent	int	N	当前页数
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	规则集列表
data.list[i].id	int	规则集ID
data.list[i].createTime	date	创建时间
data.list[i].updateTime	date	更新时间
data.list[i].presetName	String(50)	规则集名称
data.list[i].vulId	text	选择漏洞id字符串
data.list[i].languageId	int	语言ID
data.list[i].languageName	String(10)	语言名称
data.list[i].email	String(40)	创建人名称
data.list[i].createBy	String(40)	创建人ID
data.list[i].status	int	规则集状态0不可用 1可用
data.list[i].isDefault	string	默认扫描方案 0否1是
data.list[i].isThird	string	0非全量 1全量扫描方案 2精选扫描方案


*******编辑扫描方案信息
*******.1接口描述
编辑指定扫描方案信息。语言ID可通过调用加载开发语言列表获取，漏洞ID可通过调用根据多个语言查询安全规则漏洞树获取，编码规范漏洞ID可通过调用根据多个语言查询编码规范漏洞树获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：put
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vulrule/editRuleSet

*******.3输入参数

参数名	类型	必选	描述
vulRuleId	Int 	Y	规则集ID(*******查询的规则集ID)
name	string(20)	N	规则集名称
languageIds	string	Y	语言ID（4.1.1获取语言ID），多个使用逗号拼接
vulDataIds	string	Y	漏洞id集合 逗号拼接
status	Int	N	0为不生效，1为生效
desc	string(240)	N	描述
isDefault	int	N	默认扫描方案 0否1是 默认0
desc	string	N	扫描方案描述
codingIdList	string	N	编码规范漏洞id，多个使用逗号拼接，该字段取值为*******返回结果中的id-type-change,使用-拼接
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

*******删除扫描方案信息
*******.1接口描述
删除指定扫描方案信息。此接口限定企管级用户访问。
*******.2请求定义
请求方式：delete
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/vulrule/deleteRuleSet
*******.3输入参数

参数名	类型	必选	描述
vulRuleId	Int 	Y	规则集ID(4.32查询的规则集ID)
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

*******查询同组下全部规则集列表
*******.1接口描述
返回同一企业下所有可用扫描方案。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/vulrule/ruleListALL
*******.3输入参数
无
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object
data.id	int	规则集ID
data.name	String(50)	规则集名称


*******获取工具规则列表
*******.1接口描述
返回支持的工具规则列表。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: application/x-www-form-urlencoded
请求地址：http://{CS_URL}/cs/api/{VERSION}/toolRules/list
*******.3输入参数
参数名	类型	必选	描述
type	String	N	类型 内置规则、集成规则
name	String	N	工具规则名称

*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	返回数据
data.id	int	工具规则id
data.language	string	支持语言
data.name
	String	名称
data.status
	int	状态 1启用 0 停用
data.type	string	类型 内置规则、集成规则
data.desc	string	描述


4.5.5白名单管理
*******查询白名单语言列表
*******.1接口描述
返回函数白名单支持的语言列表。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/whiteFunction/findLanguageList
*******.3输入参数
无
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据
data.[i].name	String(100)	语言名字
data.[i].languageId	int	语言ID
data.[i].languageName	String(100)	上传引擎语言名字
data.[i]. createBy	String(40)	创建人ID
data.[i]. createTime	datetime	创建人时间
data.[i]. updateTime	datetime	更新时间

*******根据语言ID查询白名单对应支持漏洞信息
*******.1接口描述
返回对应语言白名单支持的漏洞数据列表。语言ID可通过调用查询白名单语言列表获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/whiteFunction/findVulList
*******.3输入参数

参数名	类型	必选	描述
languageId	Int	Y	语言ID（4.35获取语言ID）
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据
data.[i].taintFlag	String(100)	上传引擎标识
data.[i].languageId	int	语言ID
data[i]. toolUuid	String(100)	tooluuid
data[i].vulDataId	String(40)	漏洞ID
data[i].name	String(526)	漏洞名称
data[i]. createBy	String(40)	创建人ID
data[i]. createTime	datetime	创建人时间
data[i]. updateTime	datetime	更新时间

*******创建白名单
*******.1接口描述
根据所选语言和漏洞ID创建函数白名单。语言ID可通过调用查询白名单语言列表获取。漏洞ID可通过调用根据语言ID查询白名单对应支持漏洞信息获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/whiteFunction/whiteCreate
*******.3输入参数

参数名	类型	必选	描述
languageId	Int	Y	语言ID（4.35获取语言ID）
selectType	Int 	Y	是否全选：0全选漏洞 1 不全选
name	String(100)	Y	白名单名称
status	Int 	Y	是否生效 0不生效 1生效
funName	String(200)	Y	方法名
vulDataIds	List<String>	N	漏洞ID集合（当selectType为一时必填,4.36获取对应语言支持漏洞ID）
className	String(200)	N	类名/结构体
packageName	String(1000)	N	包名/命名空间
outType	String(6)	N	返回值类型
方法返回值：return
方法调用对象：object
方法参数列表：funArgs
funArgs	String(50)	N	方法参数列表
createBy	String(40)	N	创建人名称
description	String(40)	N	描述
effectiveType	Int	N	生效范围 0：全局生效（默认） 1：指定项目
projectUuids	List<String>	N	指定项目的uuid集合（当effectiveType为1时必填）
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

*******查询白名单列表
*******.1接口描述
返回函数白名单分页列表。语言ID可通过调用查询白名单语言列表获取。此接口限定企管级用户访问。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/whiteFunction/whiteList
*******.3输入参数

参数名	类型	必选	描述
name	String(100)	N	白名单name（支持迷糊查询）
languageId	int	N	语言ID（4.35获取语言ID）
pageSize	int	N	每页个数(默认页10条)
pageCurrent	int	N	当前页数（默认第1页）
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	白名单列表
data.list[i].groupId	Int 	用户组ID
data.list[i].funName	String(200)	方法名
data.list[i].vulDataIds	List<String>	（当selectType为一时必填）漏洞ID集合
data.list[i].className	String(200)	类名/结构体
data.list[i].packageName	String(1000)	包名/命名空间
data.list[i].outType	String(6)	返回值类型
方法返回值：return
方法调用对象：object
方法参数列表：funArgs
data.list[i].funArgs	String(50)	方法参数列表
data.list[i].createBy	String(100)	创建人名称
data.list[i].description	String(400)	描述
data.list[i].cusRuleId	String(42)	关联customer_rule ID
data.list[i].status	Int 	状态标识0不可用1可用
data.list[i].email	String(40)	创建人标识(邮箱)
data.list[i].name	String(40)	白名单名字
data.list[i].languageId	int	语言ID
data.list[i].id	int	白名单Id
data.list[i]. createBy	String(40)	创建人ID
data.list[i]. createTime	datetime	创建时间
data.list[i]. updateTime	datetime	更新时间
data.list[i].effectiveType	int	生效范围 0：全局生效 1：指定项目
data.list[i].projectUuids	List<String>	指定项目的uuid集合

*******修改白名单
*******.1接口描述
修改指定函数白名单。此接口限定企管级用户访问。
*******.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/whiteFunction/whiteEdit
*******.3输入参数

参数名	类型	必选	描述
languageId	Int	Y	语言ID（4.35获取语言ID）
id	int	Y	白名单ID(4.38返回白名单ID)
selectType	Int 	Y	是否全选：0全选 1 不全选
name	String（100）	Y	白名单名称
status	Int 	Y	是否生效 0不生效 1生效
funName	String(200)	Y	方法名
vulDataIds	string	N	漏洞ID集合字符串 逗号拼接（当selectType为一时必填,4.36获取对应语言支持漏洞ID）
className	String(200)	N	类名/结构体
packageName	String(1000)	N	包名/命名空间
outType	String(6)	N	返回值类型
方法返回值：return
方法调用对象：object
方法参数列表：funArgs
funArgs	String(50)	N	方法参数列表
createBy	String(100)	N	创建人名称
description	String(400)	N	描述
cusRuleId	String(40)	N	关联customer_rule ID
effectiveType	Int	N	生效范围 0：全局生效（默认） 1：指定项目
projectUuids	List<String>	N	指定项目的uuid，effectiveType为1时必填
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

*******根据ID删除白名单
*******.1接口描述
删除指定函数白名单。此接口限定企管级用户访问。
*******.2请求定义
请求方式：delete
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/whiteFunction/whiteDeleteBatch
*******.3输入参数

参数名	类型	必选	描述
funIds	string	Y	白名单ID集合字符串，逗号拼接(4.38返回白名单ID)
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

4.6系统管理
4.6.1系统信息
*******获取当前版本
*******.1接口描述
获取当前平台的版本号。
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/getVersion

*******.3输入参数
无
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.version	string(10)	当前版本


*******获取license信息
*******.1接口描述
获取license绑定的授权信息：机器码、支持扫描的编程语言、支持的最大项目数、项目同时扫描的最大并发数、最大代码行数、license过期时间、维护到期时间、是否可以使用编码规范、是否可以使用静态扫描、是否可以使用动态扫描等。
*******.2请求定义
请求方式：post
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/license/getLicenseInfo
*******.3输入参数
无
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.macCode	string(64)	机器码
data.languageList	List	可用语言
data.projectNum	Int	最大项目数
data.maxCurrent	Int	最大并发数
data.maxCodeLine	Int	最大代码行数
data.expireTime	string(20)	license过期时间
data.ensureTime	string(20)	维护到期时间
data.codingRulesScanStatus	Boolean	是否可以使用编码规范
data.staticRulesScanStatus	Boolean	是否可以使用静态扫描
data.dynamicScanStatus	Boolean	是否可以使用动态扫描


*******获取引擎服务器信息
*******.1接口描述
获取引擎服务器列表信息
*******.2请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/server/list
*******.3输入参数
无
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.id	int	服务器id
data.address	string(64)	引擎服务器地址
data.status	Int	状态0不可用 1可用
data.auth	Int	License状态 1 已认证 2 未认证
data.maxCurrent	Int	最大并发数
data.version	string(20)	版本
data.performanceLevel	Int	性能等级 0 普通 1高性能
data.codingRulesScanStatus	Int	是否可以使用编码规范 0 否 1是
data.staticCodeScanStatus	Int	是否可以使用静态扫描 0 否 1 是
data.dynamicCodeScanStatus	Int	是否可以使用动态扫描 0 否 1 是
data.desc	String(64)	连接状态描述 失败时不为空


4.6.2告警管理
告警功能主要用于对指定的指标达到设定的临界值时，通过系统通知或邮件对用户进行提醒。方便用户及时感知系统状况，并及时采取相应措施。
*******新增告警条件配置信息
*******.1接口描述
增加要新增监控的指标，包含：
项目风险值、超危缺陷数、高危缺陷数、中危缺陷数、低危缺陷数、代码重复率、圈复杂度超标个数、建议缺陷数、超危编码缺陷、高危编码缺陷、中危编码缺陷、低危编码缺陷、建议编码缺陷。
同时可指定发送告警的方式、接收用户角色或指定用户（UUID）。此接口限定企管级用户访问。
*******.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/alarmConfig/addAlarmConfig
仅限企业管理员访问
*******.3输入参数

参数名	类型	必选	描述
status	Int	Y	状态
type	Int 	Y	适用对象 0:针对指定团队 1:针对所有团队
alarmType	String(5)	Y	告警方式 1.邮件 2.系统通知 3.短信
alarmTrigger	Int 	Y	触发通知条件:  1：分数为触发条件  2：严重漏洞数为触发条件  3：高危漏洞数为触发条件 4：复合触发条件
receiveUser	String(400)	N	接收告警的指定用户的uuid （角色为99时必填）
receiveRole	String(10)	Y	接收告警的角色  2:企业管理员 3:审计人员 5: 团队管理员 99:指定成员
checkOrg	String(400)	N	指定检查的团队的uuid （适用对象为0时必填）
taskStatus	Int	N	扫描任务状态：0：失败 1：成功
score	Int	N	项目风险值
criticalNum	Int	N	严重漏洞数
highNum	Int	N	高危漏洞数
mediumNum	Int	N	中危漏洞数
lowNum	Int	N	低危漏洞数
noteNum	Int	N	建议漏洞数
cyclomaticComplexityNum	Int	N	圈复杂度超标个数
repetitiveRate	Int	N	代码重复率（%） （最大为100）
codingCriticalNum	Int	N	严重编码缺陷
codingHighNum	Int	N	高危编码缺陷
codingMediumNum	Int	N	中危编码缺陷
codingLowNum	Int	N	低危编码缺陷
codingNoteNum	Int	N	建议编码缺陷
mailLang	string	N	邮件默认语言，默认中文
简体中文 zh-CN 繁体中文 zh-TW English(US) en-US
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据


******* 修改告警条件配置信息
*******.1接口描述
对已设置的告警配置信息进行修改。此接口限定企管级用户访问。
*******.2请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/alarmConfig/modifyAlarmConfig
仅限企业管理员访问
*******.3输入参数
参数名	类型	必选	描述
Id	Int	Y	欲修改的配置id
status	Int	Y	状态
type	Int 	Y	适用对象类型 1:针对单个项目  2:针对团队 3:针对企业
alarmType	String(5)	Y	告警方式 1.邮件 2.系统通知 3.短信
alarmTrigger	Int 	Y	触发通知条件:  1：分数为触发条件  2：严重漏洞数为触发条件  3：高危漏洞数为触发条件 4：复合触发条件
receiveUser	String(400)	N	接收告警的指定用户的uuid （角色为99时必填）
receiveRole	String(10)	Y	接收告警的角色  2:企业管理员 3:审计人员 5: 团队管理员 99:指定成员
checkOrg	String(400)	N	指定检查的团队的uuid （适用对象为0时必填）
taskStatus	Int	N	扫描任务状态：0：失败 1：成功
score	Int	N	项目风险值
criticalNum	Int	N	严重漏洞数
highNum	Int	N	高危漏洞数
mediumNum	Int	N	中危漏洞数
lowNum	Int	N	低危漏洞数
noteNum	Int	N	建议漏洞数
cyclomaticComplexityNum	Int	N	圈复杂度超标个数
repetitiveRate	Int	N	代码重复率（%）（最大为100）
codingCriticalNum	Int	N	严重编码缺陷
codingHighNum	Int	N	高危编码缺陷
codingMediumNum	Int	N	中危编码缺陷
codingLowNum	Int	N	低危编码缺陷
codingNoteNum	Int	N	建议编码缺陷
mailLang	string	N	邮件默认语言，默认中文
简体中文 zh-CN 繁体中文 zh-TW English(US) en-US
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	List	返回数据


******* 查询告警条件配置信息
*******.1接口描述
分页查询已配置的警告条件信息。此接口限定企管级用户访问。
*******.2请求定义
请求方式：Get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/alarmConfig/listAlarmConfig
仅限企业管理员访问
*******.3输入参数
参数名	类型	必选	描述
pageSize	int	N	每页个数(默认10条)
pageCurrent	int	N	当前页数（默认第1页）
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	告警配置信息列表
data.list[i].id	Int	配置id
data.list[i].createTime	String(20)	创建时间
data.list[i].updateTime	String(20)	修改时间
data.list[i].status	Int	状态
data.list[i].type	Int 	适用对象类型 1:针对单个项目  2:针对团队 3:针对企业
data.list[i].alarmType	String(5)	告警方式 1.邮件 2.系统通知 3.短信
data.list[i].alarmTrigger	Int 	触发通知条件:  1：分数为触发条件  2：严重漏洞数为触发条件  3：高危漏洞数为触发条件 4：复合触发条件
data.list[i].receiveUser	String(400)	接收告警的指定用户的uuid
data.list[i].receiveRole	String(10)	接收告警的角色  2:企业管理员 3:审计人员 5: 团队管理员 99:指定成员
data.list[i].checkOrg	String(400)	指定检查的团队的uuid
data.list[i].groupId	Int	企业Id
data.list[i].taskStatus	Int	扫描任务状态：0：失败 1：成功
data.list[i].score	Int	项目风险值
data.list[i].criticalNum	Int	严重漏洞数
data.list[i].highNum	Int	高危漏洞数
data.list[i].mediumNum	Int	中危漏洞数
data.list[i].lowNum	Int	低危漏洞数
data.list[i].noteNum	Int	建议漏洞数
data.list[i].cyclomaticComplexityNum	Int	圈复杂度超标个数
data.list[i].repetitiveRate	Int	代码重复率（%）（最大为100）
data.list[i].codingCriticalNum	Int	严重编码缺陷
data.list[i].codingHighNum	Int	高危编码缺陷
data.list[i].codingMediumNum	Int	中危编码缺陷
data.list[i].codingLowNum	Int	低危编码缺陷
data.list[i].codingNoteNum	Int	建议编码缺陷
data.list[i].mailLang	String	邮件默认语言
简体中文 zh-CN 繁体中文 zh-TW English(US) en-US



4.6.2.4 删除告警条件配置信息
4.6.2.4.1接口描述
如不再需要监控告警时，可根据配置Id进行删除。配置Id可用接口（查询告警条件配置信息）中获取。此接口限定企管级用户访问。
4.6.2.4.2请求定义
请求方式：Delete
请求头类型：Content-Type: application/ form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/alarmConfig/ batchDeleteAlarmConfig
仅限企业管理员访问
4.6.2.4.3输入参数
参数名	类型	必选	描述
configIdList	string	Y	欲删除的配置id集合（逗号拼接）
4.6.2.4.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data
	List	返回数据

4.6.3凭证管理
凭据是访问其他系统的认证信息，CodeSec可以通过设置凭证与其他第三方应用进行认证，在可信和可控的范围内，完成第三方交互。在系统配置增加凭据管理用于设置凭证与其他第三方应用进行认证，凭证管理具体通过API Token方式进行认证，并支持对凭证的使用权限进行配置。配置完成后，创建检测项目时可选择对应的凭证进行认证和拉取代码操作。
*******查询凭证信息
*******.1接口描述
分页查询已配置的凭证信息。此接口限定企管、团管和普通用户访问。
*******.2请求定义
请求方式：Get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/project/getVoucherList
此接口限定企管、团管和普通用户访问。
*******.3输入参数
参数名	类型	必选	描述
pageSize	int	N	每页个数(默认10条)
pageCurrent	int	N	当前页数（默认第1页）
name	String	N	名称
type	int	N	仓库类型
*******.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object
data.total	int	总共有多少条数据
data.pageTotal	int	总共有多少页
data.pageCurrent	int	当前页
data.pageSize	int	一页查多少条
data.list	List	告警配置信息列表
data.list[i].id	Int	配置id
data.list[i].name	String(100)	名称
data.list[i].type	Int	仓库类型
data.list[i].description	Int	描述

4.7用户管理
4.7.1创建账号
*******接口描述
创建账号，返回用户API访问KEY和密钥，用于接口的调用。创建的用户被分配到接口请求用户所在的企业。
*******请求定义
请求方式：post
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/user/createAccount
*******输入参数
参数名	类型	必选	描述
userName	string(50)	Y	用户名
userEmail	string(512)	Y	用户邮箱（登录账号）
password	string(80)	Y	用户密码（请使用sha256加密传输）
phone	string(20)	N	手机号
countryCode	string(10)	N	国际区号（手机号不为空时，国际区号必填）
keyExpiredDate	string(20)	N	秘钥过期时间（yyyy-MM-dd HH:mm:ss默认2099-01-01 00:00:00）

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.accessKey	string(40)	用户API访问KEY（可做唯一标识）
data.accessSecret	string(2048)	秘钥（默认到2099年过期）

4.7.2删除账号
4.7.2.1接口描述
根据用户Id删除账号。删除账号后，对应的API访问KEY不可用，接口调用鉴权会失败，已登录账号会强制退出。
4.7.2.2请求定义
请求方式：delete
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/user/{userId}/deleteAccount
4.7.2.3输入参数
请求地址中的userId为要删除的用户的API访问KEY（accessKey）
4.7.2.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在

4.7.3更新秘钥
4.7.3.1接口描述
为账号生成的密钥会有一个过期时间，过期后，接口调用将会失败。在密钥快要过期时，用户需要提前调用此接口，重新生成密钥和设置过期时间。
注：此接口调用需要使用旧密钥进行鉴权，请确保过期前进行调用更新，否则联系管理员在后台设置。
4.7.3.2请求定义
请求方式：put
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/user/{userId}/updateAccessSecrete
4.7.3.3输入参数
参数名	类型	必选	描述
keyExpiredDate	string(20)	N	秘钥过期时间（yyyy-MM-dd HH:mm:ss默认2099-01-01 00:00:00）
请求地址中的userId为要更新的用户的API访问KEY（accessKey）
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.accessSecret	string(2048)	秘钥

4.7.4查询当前用户团队列表
*******接口描述
查询当前用户所属团队信息
*******请求定义
请求方式：get
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/user/getOrgList
*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.orgList	list	团队信息列表


团队信息列表对象
参数名	类型	描述
orgUuid	String(40)	团队uuid
orgName	String(128)	团队名称
roleId	int(10)	角色id
roleName	String(64)	角色名称
roleDesc	String(128)	角色描述

4.7.5根据团队UUID切换团队
*******接口描述
根据团队uuid切换团队
*******请求定义
请求方式：post
请求头类型：Content-Type: multipart/form-data
请求地址：http://{CS_URL}/cs/api/{VERSION}/user/changeOrg
*******输入参数
参数名	类型	描述
orgUuid	string(40)	团队uuid（查询当前用户团队列表 接口中的data.orgList[i].orgUuid）

*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息

4.8首页缺陷统计
4.8.1缺陷数量统计
*******接口描述
返回安全等级缺陷数量统计汇总数据（对应系统首页界面“缺陷数量统计”）。
*******请求定义
请求方式：get
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/dashboard/countVulStatistics
*******输入参数
无
*******输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data.vulNum	Int	缺陷数量总计
data.vulCriticalNum	Int	超危缺陷数量
data.vulHighNum	Int	高危缺陷数量
data.vulMediumNum	Int	中危缺陷数量
data.vulLowNum	Int	低危缺陷数量
vulDensity	Number	缺陷密度
repeatRate	Number	代码重复率
complexity	Number	圈复杂度
thirdPartNum	Int	第三方组件数量
cveNum	Int	CVE数量
cnnvdNum	Int	CNNVD数量
data.vulNoteNum	Int	建议缺陷数量
data. vulTypeCounts	object	缺陷数量明细对象

vulTypeCounts对象：
参数名	类型	描述
typeName	string(20)	缺陷分类
typeNum	Int	缺陷数量

4.8.2开发语言超、高危缺陷统计（TOP5）
4.8.2.1接口描述
返回代码语言超危和高危缺陷前五统计数据（对应系统首页界面“开发语言超、高危缺陷统计（TOP5）”）。
4.8.2.2请求定义
请求方式：get
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/dashboard/languageTop5Statistics
4.8.2.3输入参数
无
4.8.2.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data[0].name	string(40)	语言名称
data[0].vulNum	Int	缺陷数量

4.8.3超、高危缺陷类型统计（TOP5）
4.8.3.1接口描述
返回超危和高危不同缺陷类型统计数据（对应系统首页界面“超、高危缺陷类型统计（TOP5）”）。
4.8.3.2请求定义
请求方式：get
请求头类型：Content-Type: application/json
请求地址：http://{CS_URL}/cs/api/{VERSION}/dashboard/vulTypeTop5Statistics
4.8.3.3输入参数
无
4.8.3.4输出参数
参数名	类型	描述
status	boolean	状态
message	string(512)	描述信息
data	object	当返回成功的时候，data内容存在
data[0].name	string(40)	缺陷类型
data[0].vulNum	Int	缺陷数量

