package codesec

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gitlab.com/piccolo_su/vegeta/cmd/portal/utils"
	"gitlab.com/security-rd/go-pkg/logging"
)

// ScanResultParam 获取扫描结果参数
type ScanResultParam struct {
	AccessKey    string `json:"accessKey"`
	AccessSecret string `json:"accessSecret"`
	ProjectUuid  string `json:"projectUuid"`
	ScanId       string `json:"scanId"`
}

// ScanResultResponse 扫描结果响应
type ScanResultResponse struct {
	Status  bool        `json:"status"`
	Message string      `json:"message"`
	Data    ScanResult  `json:"data"`
}

// ScanResult 扫描结果
type ScanResult struct {
	ScanId        string    `json:"scanId"`
	ProjectUuid   string    `json:"projectUuid"`
	Status        int       `json:"status"` // 0: 未扫描, 1: 扫描中, 2: 扫描完成, 3: 扫描失败
	VulCriticalNum int      `json:"vulCriticalNum"`
	VulHighNum    int       `json:"vulHighNum"`
	VulMediumNum  int       `json:"vulMediumNum"`
	VulLowNum     int       `json:"vulLowNum"`
	VulNoRiskNum  int       `json:"vulNoRiskNum"`
	VulTotalNum   int       `json:"vulTotalNum"`
	ScanTime      int64     `json:"scanTime"`
	VulList       []VulInfo `json:"vulList"`
}

// VulInfo 漏洞信息
type VulInfo struct {
	VulId       string `json:"vulId"`
	VulName     string `json:"vulName"`
	VulLevel    int    `json:"vulLevel"` // 1: 低危, 2: 中危, 3: 高危, 4: 超危
	FilePath    string `json:"filePath"`
	LineNumber  int    `json:"lineNumber"`
	Description string `json:"description"`
	Solution    string `json:"solution"`
}

// GetScanResult 获取扫描结果
func GetScanResult(baseUrl string, param ScanResultParam) (*ScanResultResponse, error) {
	// 定义请求URL
	apiURL := fmt.Sprintf("%s/%s", baseUrl, "cs/api/v4/scan/result")

	// 创建HTTP请求
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	header := utils.BuildV2Header(nil, param.AccessKey, param.AccessSecret, []string{param.ProjectUuid, param.ScanId})
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 设置查询参数
	q := req.URL.Query()
	q.Add("projectUuid", param.ProjectUuid)
	q.Add("scanId", param.ScanId)
	req.URL.RawQuery = q.Encode()

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &ScanResultResponse{}
	all, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logging.Get().Info().Str("url", apiURL).Str("res", string(all)).Msg("Codesec scan result resp")
	if err := json.Unmarshal(all, response); err != nil {
		return nil, err
	}
	if !response.Status {
		return nil, fmt.Errorf("get scan result failed: %s", response.Message)
	}
	return response, nil
}

// StartScan 启动扫描
func StartScan(baseUrl string, param ScanResultParam) (*ScanResultResponse, error) {
	// 定义请求URL
	apiURL := fmt.Sprintf("%s/%s", baseUrl, "cs/api/v4/scan/start")

	// 创建请求体
	reqBody := map[string]string{
		"projectUuid": param.ProjectUuid,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	header := utils.BuildV2Header(reqBody, param.AccessKey, param.AccessSecret, []string{param.ProjectUuid})
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &ScanResultResponse{}
	all, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logging.Get().Info().Str("url", apiURL).Str("res", string(all)).Msg("Codesec start scan resp")
	if err := json.Unmarshal(all, response); err != nil {
		return nil, err
	}
	if !response.Status {
		return nil, fmt.Errorf("start scan failed: %s", response.Message)
	}
	return response, nil
}
