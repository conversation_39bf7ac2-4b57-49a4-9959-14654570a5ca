server {
	listen 80;
	server_name codesec.tensorsecurity.cn;
	return 301 https://$server_name$request_uri;
}
server {
	#listen 80;
	listen 443 ssl;
	client_max_body_size 0;
    proxy_set_header  Host $http_host;
	proxy_set_header  X-Real-IP $remote_addr;
	proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
	proxy_set_header  X-Forwarded-Proto $scheme;
	proxy_set_header  X-Forwarded-Uri $request_uri;

	server_name codesec.tensorsecurity.cn;


    location / {
        	proxy_pass http://***********:28081;
        	proxy_hide_header X-Frame-Options;  # 移除 X-Frame-Options 头
	}

	access_log /data/nginx/logs/codesec.log;
	error_log /data/nginx/logs/codesec.log;

	ssl_certificate      /etc/pki/nginx/tensorsec/tls.crt;
	ssl_certificate_key  /etc/pki/nginx/tensorsec/tls.key;
	ssl_session_cache   shared:SSL:1m;
	ssl_session_timeout 5m;
	ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
	ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
	ssl_prefer_server_ciphers on;
	underscores_in_headers on;
}