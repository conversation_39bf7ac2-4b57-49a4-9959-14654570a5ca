apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
  labels:
    app: portal-backend
    app.kubernetes.io/component: portal
    app.kubernetes.io/instance: navigator
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: portal-backend
    helm.sh/chart: portal-0.1.0
    version: 0.1.0
  name: tensorsec-portal-backend
  namespace: tensorsec
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/component: portal-backend
      app.kubernetes.io/instance: navigator
      app.kubernetes.io/name: portal-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: portal-backend
        app.kubernetes.io/component: portal-backend
        app.kubernetes.io/instance: navigator
        app.kubernetes.io/name: portal-backend
        election.status: leader
        version: 0.1.0
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/component: portal-backend
                    app.kubernetes.io/instance: navigator
                    app.kubernetes.io/name: portal-backend
                namespaces:
                  - tensorsec
                topologyKey: kubernetes.io/hostname
              weight: 1
      containers:
        - image: ************:8080/tensorsecurity/portal-backend:cluster02
          imagePullPolicy: Always
          name: portal-backend
          livenessProbe:
            failureThreshold: 5
            initialDelaySeconds: 10
            periodSeconds: 5
            successThreshold: 1
            tcpSocket:
              port: 10800
            timeoutSeconds: 5
          ports:
            - containerPort: 10800
              name: portal-backend
              protocol: TCP
          args:
            - --config-path=/etc/portal-config/portal-config.yaml
          command:
            - ./portal
          env:
            - name: MY_POD_APP_LABEl
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app']
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: common-env
            - secretRef:
                name: common-secret
          resources:
            limits:
              cpu: 250m
              memory: 250Mi
            requests:
              cpu: 250m
              memory: 250Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: config-volume
              mountPath: /etc/portal-config
              readOnly: true
      volumes:
        - name: config-volume
          configMap:
            name: portal-config
            items:
              - key: portal-config.yaml
                path: portal-config.yaml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: harbor-admin-secret
      restartPolicy: Always
      schedulerName: default-scheduler


---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: portal-backend
    app.kubernetes.io/name: portal-backend
  name: portal-backend
  namespace: tensorsec
spec:
  selector:
    app: portal-backend  # 必须匹配您的 Deployment 的标签
  ports:
    - name: http
      port: 10800
      protocol: TCP
      targetPort: 10800
  type: ClusterIP

