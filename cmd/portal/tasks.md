我现在需要做 portal 项目的功能，项目目录是：cmd/portal
我需要实现很多功能，现在开始一项一项的实现
如果在改动过程中有表的变动，直接改cmd/portal/store/up.sql 这个文件

现在开现实现如下功能：
Project需要有：最近扫描时间，最近更新时间，创建人，更新人 这四个字段
1，创建人，更新人，从 token 中 获取邮箱，在新建项目，更新项目时要增加这几个字段
2：返回的列表中需要有这几个字段
3：增加一个接口：项目详情接口

现在开始实现如下功能：
获取codesec对一个项目进行代码扫描后的结果，获取后存入 portal 项目中
codesec 是另外的一个项目，他的接口文档是：cmd/portal/component/codesec/codesec_v4.docx
我已经写了一部分 codesec 的接口可以供你参考，路径是：cmd/portal/component/codesec/project.go
Project 表中codesec_uuid表示 codesec的项目 uuid
1,分页查询漏洞列表



需要实现的接口：如下
项目详情接口



codesec漏洞列表
sourceCheck扫描信息
sourceCheck漏洞列表
sourceCheck许可列表
sourceCheck组件列表
镜像扫描列表
增加白名单
镜像详情页面跳转
partal扫描任务管理
partal项目发起扫描
项目列表中增加扫描状态字段
向codesec发起扫描
向sourcecheck发起扫描
持续查询codesec扫描状态，更新portal扫描状态
持续查询sourcecheck扫描状态，更新portal扫描状态
扫描成功之后查看codesec的扫描结果，并更新项目列表中的风险状态
扫描成功之后查看sourceCheck的扫描结果，并更新项目列表中的风险状态

新建导出任务
导出任务列表接口
获取CI的镜像基本信息
获取CI的扫描漏洞数据
获取CI的扫描软件包
获取CI的扫描敏感文件
获取codesec漏洞列表
获取sourceCheck漏洞列表
获取sourceCheck许可列表
获取sourceCheck组件列表
拼装数据成excel，并压缩成zip文件
压缩文件，保存到本地
提供文件下载接口