package warehouse

import (
	"bytes"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/docker/distribution/manifest/schema2"
	registry2 "github.com/heroku/docker-registry-client/registry"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
	"gitlab.com/security-rd/go-pkg/logging"

	imagesecModel "gitlab.com/piccolo_su/vegeta/pkg/model/imagesec"
)

func ClientLogFormatter(format string, args ...interface{}) {
	logging.Get().Trace().Msgf(format, args...)
}

func NewDockerRegistryClient(url, userName, password string, skipTLSVerify bool) (*registry2.Registry, error) {
	hub, err := registry2.New(url, userName, password)
	if err != nil && skipTLSVerify {
		// Check for any type of error defined in x509 package.
		ok1 := errors.As(err, &x509.SystemRootsError{})
		ok2 := errors.As(err, &x509.CertificateInvalidError{})
		ok3 := errors.As(err, &x509.UnknownAuthorityError{})
		ok4 := errors.As(err, &x509.HostnameError{})
		if ok1 || ok2 || ok3 || ok4 {
			logging.Get().Warn().Msg("Certificate validation failed, but insecure option is on - will retry and skip TLS cert verification")
			hub, err = registry2.NewInsecure(url, userName, password)
		}
	}
	if err != nil {
		logging.Get().Err(err).Msg("new registry client failed.")
		return nil, err
	}
	hub.Logf = ClientLogFormatter
	return hub, nil
}

type Hash struct {
	// Algorithm holds the algorithm used to compute the hash.
	Algorithm string

	// Hex holds the hex portion of the content hash.
	Hex string
}

func (h Hash) String() string {
	return fmt.Sprintf("%s:%s", h.Algorithm, h.Hex)
}

func SHA256(r io.Reader) (Hash, int64, error) {
	hasher := sha256.New()
	n, err := io.Copy(hasher, r)
	if err != nil {
		return Hash{}, 0, err
	}
	return Hash{
		Algorithm: "sha256",
		Hex:       hex.EncodeToString(hasher.Sum(make([]byte, 0, hasher.Size()))),
	}, n, nil
}

func GetRegistryDriver(reg imagesecModel.Registry) (Registry, error) {

	drive, err := Open(RegToRegistryConf(reg))
	if err != nil {
		logging.Get().Err(err).Str("module", "RegistryImage").Str("name", reg.Name).Msg("sync image open registry")
		return nil, err
	}
	if err := drive.Ping(); err != nil {
		logging.Get().Err(err).Str("module", "RegistryImage").Str("regName", reg.Name).Msg("connect registry")
		return nil, err
	}
	logging.Get().Debug().Str("module", "RegistryImage").Interface("reg", reg).Msg("getRegistryDriver")

	return drive, nil
}

func RegToRegistryConf(reg imagesecModel.Registry) RegistrableComponentConfig {
	opt := make(map[string]interface{})
	opt["type"] = reg.RegType
	opt["registry_id"] = reg.ID
	opt["url"] = reg.Url
	opt["username"] = reg.Username
	opt["password"] = reg.PasswordString
	opt["skip_tls_verify"] = true
	opt["insecure"] = true
	opt["access_key"] = reg.AccessKey
	opt["access_secret"] = reg.AccessSecret
	opt["instance_id"] = reg.InstanceID
	opt["region_id"] = reg.RegionID

	if reg.RegType == imagesecModel.HaiWeiSwrVersion {
		opt["access_key"] = reg.Username
		opt["secret_key"] = reg.PasswordString
		opt["username"] = ""
		opt["password"] = ""
	}

	conf := RegistrableComponentConfig{
		Type:    reg.RegType,
		Options: opt,
	}
	logging.Get().Debug().Str("module", "RegistryImage").Interface("reg", opt).Msg("RegToRegistryConf")
	return conf
}

func pullImageManifest(cli *registry2.Registry, repo, digest string, mediaType string) (*schema2.Manifest, []byte, error) {
	// 使用自定义的 Accept 头来支持 v2 manifest
	url := Url(cli, "/v2/%s/manifests/%s", repo, digest)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, nil, err
	}

	// 添加多种媒体类型支持
	req.Header.Add("Accept", mediaType)

	// 使用 RegistryClient 的 Client 发送请求
	// 注意：RegistryClient 已经处理了认证，所以这里不需要手动添加
	resp, err := cli.Client.Do(req)
	if err != nil {
		logging.Get().Err(err).Msg("pull image manifest")
		return nil, nil, err
	}
	defer util.CloseBodyWithLog(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logging.Get().Err(err).Msg("pull image can not read body")
		return nil, nil, err
	}

	manifest := &schema2.Manifest{}
	if err := json.Unmarshal(body, manifest); err != nil {
		logging.Get().Info().Str("mediaType", mediaType).Str("body", string(body)).
			Str("repo", repo).Str("digest", digest).Msg("PullImageManifest get body")
		logging.Get().Err(err).Msg("pull image can not unmarshall")
		return nil, nil, err
	}

	return manifest, body, nil
}

// DeserializedManifest 自定义的反序列化 Manifest 结构体，绕过 MediaType 检查
type DeserializedManifest struct {
	schema2.Manifest
	// canonical is the canonical byte representation of the Manifest.
	canonical []byte
}

func (m *DeserializedManifest) MarshalJSON() ([]byte, error) {
	if len(m.canonical) > 0 {
		return m.canonical, nil
	}
	return json.Marshal(m.Manifest)
}

// UnmarshalJSON 自定义反序列化方法，绕过 MediaType 检查
func (m *DeserializedManifest) UnmarshalJSON(b []byte) error {
	m.canonical = make([]byte, len(b))
	copy(m.canonical, b)

	// 直接反序列化到 Manifest 结构体，不进行 MediaType 检查
	return json.Unmarshal(b, &m.Manifest)
}

func PullImageManifestV2(cli *registry2.Registry, repo, digest string) (*DeserializedManifest, error) {
	header := []string{
		"application/vnd.docker.distribution.manifest.v2+json",
		"application/vnd.oci.image.manifest.v1+json",
		"application/vnd.docker.distribution.manifest.list.v2+json",
		"application/vnd.oci.image.index.v1+json",
		"application/vnd.docker.distribution.manifest.v1+json",
	}
	for _, h := range header {
		manifest, body, err := pullImageManifest(cli, repo, digest, h)
		if err == nil && manifest != nil {
			logging.Get().Info().Str("image", repo+"/"+digest).Str("header", h).Msg("pull image PullImageManifest")

			// 创建自定义的 DeserializedManifest，绕过 MediaType 检查
			deserializedManifest := &DeserializedManifest{
				Manifest:  *manifest,
				canonical: body,
			}

			return deserializedManifest, nil
		}
		logging.Get().Info().Str("image", repo+"/"+digest).Str("header", h).Msg("not pull image PullImageManifest")
	}
	return nil, fmt.Errorf("not found manifest")
}

func ManifestV2Digest(body []byte) (string, error) {
	// calculate image digest
	dig, _, err := SHA256(bytes.NewReader(body))
	if err != nil {
		return "", err
	}
	return dig.String(), nil
}

func Url(cli *registry2.Registry, pathTemplate string, args ...interface{}) string {
	pathSuffix := fmt.Sprintf(pathTemplate, args...)
	url := fmt.Sprintf("%s%s", cli.URL, pathSuffix)
	return url
}
