package warehouse

import (
	"testing"

	"github.com/docker/distribution/manifest/schema2"
)

func TestDeserializedManifest_UnmarshalJSON(t *testing.T) {
	// 测试用例1: 标准的 Docker manifest v2
	dockerManifestJSON := `{
		"schemaVersion": 2,
		"mediaType": "application/vnd.docker.distribution.manifest.v2+json",
		"config": {
			"mediaType": "application/vnd.docker.container.image.v1+json",
			"size": 1234,
			"digest": "sha256:abcd1234"
		},
		"layers": [
			{
				"mediaType": "application/vnd.docker.image.rootfs.diff.tar.gzip",
				"size": 5678,
				"digest": "sha256:efgh5678"
			}
		]
	}`

	// 测试用例2: OCI manifest v1 (这种格式可能会导致原始的 schema2.DeserializedManifest 失败)
	ociManifestJSON := `{
		"schemaVersion": 2,
		"mediaType": "application/vnd.oci.image.manifest.v1+json",
		"config": {
			"mediaType": "application/vnd.oci.image.config.v1+json",
			"size": 1234,
			"digest": "sha256:abcd1234"
		},
		"layers": [
			{
				"mediaType": "application/vnd.oci.image.layer.v1.tar+gzip",
				"size": 5678,
				"digest": "sha256:efgh5678"
			}
		]
	}`

	testCases := []struct {
		name         string
		manifestJSON string
		expectError  bool
	}{
		{
			name:         "Docker Manifest V2",
			manifestJSON: dockerManifestJSON,
			expectError:  false,
		},
		{
			name:         "OCI Manifest V1",
			manifestJSON: ociManifestJSON,
			expectError:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试我们的自定义 DeserializedManifest
			customManifest := &DeserializedManifest{}
			err := customManifest.UnmarshalJSON([]byte(tc.manifestJSON))

			if tc.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tc.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if !tc.expectError {
				// 验证数据是否正确解析
				if customManifest.SchemaVersion != 2 {
					t.Errorf("Expected SchemaVersion 2, got %d", customManifest.SchemaVersion)
				}
				if customManifest.Config.Digest.String() != "sha256:abcd1234" {
					t.Errorf("Expected config digest sha256:abcd1234, got %s", customManifest.Config.Digest.String())
				}
				if len(customManifest.Layers) != 1 {
					t.Errorf("Expected 1 layer, got %d", len(customManifest.Layers))
				}

				// 测试 MarshalJSON 方法
				data, err := customManifest.MarshalJSON()
				if err != nil {
					t.Errorf("MarshalJSON failed: %v", err)
				}
				if len(data) == 0 {
					t.Errorf("MarshalJSON returned empty data")
				}
			}

			// 对比：测试原始的 schema2.DeserializedManifest 是否会失败
			originalManifest := &schema2.DeserializedManifest{}
			originalErr := originalManifest.UnmarshalJSON([]byte(tc.manifestJSON))

			t.Logf("Original schema2.DeserializedManifest error for %s: %v", tc.name, originalErr)
			t.Logf("Custom DeserializedManifest error for %s: %v", tc.name, err)
		})
	}
}

func TestManifestV2Digest(t *testing.T) {
	manifestJSON := `{
		"schemaVersion": 2,
		"mediaType": "application/vnd.docker.distribution.manifest.v2+json",
		"config": {
			"mediaType": "application/vnd.docker.container.image.v1+json",
			"size": 1234,
			"digest": "sha256:abcd1234"
		},
		"layers": [
			{
				"mediaType": "application/vnd.docker.image.rootfs.diff.tar.gzip",
				"size": 5678,
				"digest": "sha256:efgh5678"
			}
		]
	}`

	digest, err := ManifestV2Digest([]byte(manifestJSON))
	if err != nil {
		t.Errorf("ManifestV2Digest failed: %v", err)
	}
	if digest == "" {
		t.Errorf("ManifestV2Digest returned empty digest")
	}
	if len(digest) != 71 { // sha256: + 64 hex characters
		t.Errorf("Expected digest length 71, got %d", len(digest))
	}
	if digest[:7] != "sha256:" {
		t.Errorf("Expected digest to start with 'sha256:', got %s", digest[:7])
	}

	t.Logf("Generated digest: %s", digest)
}
