apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "18"
  labels:
    app: waf-operator
    app.kubernetes.io/instance: navigator
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: waf-operator
    helm.sh/chart: waf-operator-0.1.0
    version: 0.1.0
  name: tensorsec-waf-operator
  namespace: tensorsec
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/instance: navigator
      app.kubernetes.io/name: waf-operator
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: waf-operator
        app.kubernetes.io/instance: navigator
        app.kubernetes.io/name: waf-operator
        version: 0.1.0
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchLabels:
                  app.kubernetes.io/instance: navigator
                  app.kubernetes.io/name: waf-operator
              namespaces:
              - tensorsec
              topologyKey: kubernetes.io/hostname
            weight: 1
      containers:
      - args:
        - --wasm-url=http://prod-holmes-driver.tensorsec.svc.cluster.local:80/waf/waf_filter.wasm
        env:
        - name: MY_POD_APP_LABEl
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['app']
        - name: MY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: MY_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        envFrom:
        - configMapRef:
            name: common-env
        image: harbor.tensorsecurity.com/tensorsecurity/waf-operator:testcn
        imagePullPolicy: Always
        name: waf-operator
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/secrets/cluster-admin
          name: cluster-admin-secret
          readOnly: true
        - mountPath: /etc/waf
          name: waf-rules
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: harbor-admin-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: tensorsec-security-operator
      serviceAccountName: tensorsec-security-operator
      terminationGracePeriodSeconds: 30
      volumes:
      - name: cluster-admin-secret
        secret:
          defaultMode: 420
          secretName: tensorsec-security-operator
      - configMap:
          defaultMode: 420
          name: waf-rules
        name: waf-rules

