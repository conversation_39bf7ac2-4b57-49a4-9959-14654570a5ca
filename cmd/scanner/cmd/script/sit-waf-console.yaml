apiVersion: apps/v1
kind: Deployment
metadata:
  name: waf-console
spec:
  selector:
    matchLabels:
      app: waf-console
  template:
    metadata:
      labels:
        app: waf-console
    spec:
      containers:
      - name: waf-console
        image: waf-console:latest
        resources:
          limits:
            memory: "128Mi"
            cpu: "500m"
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: waf-console-config
          mountPath: /
        - name: waf-console-rules
          mountPath: /rules
      volumes:
      - name: waf-console-config
        configMap:
          name: waf-console-config
      - name: waf-console-rules
        configMap:
          name: waf-console-rules
---
apiVersion: v1
kind: Service
metadata:
  name: waf-console
spec:
  selector:
    app: waf-console
  ports:
  - port: 8080
    targetPort: 8080
