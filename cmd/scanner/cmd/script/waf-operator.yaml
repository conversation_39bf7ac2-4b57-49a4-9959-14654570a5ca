apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    meta.helm.sh/release-name: navigator
    meta.helm.sh/release-namespace: tensorsec
  labels:
    app: waf-operator
    app.kubernetes.io/instance: navigator
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: waf-operator
    helm.sh/chart: waf-operator-0.1.0
    version: 0.1.0
  name: tensorsec-waf-operator
  namespace: tensorsec
spec:
  progressDeadlineSeconds: 600
  replicas: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/instance: navigator
      app.kubernetes.io/name: waf-operator
  template:
    metadata:
      labels:
        app: waf-operator
        app.kubernetes.io/instance: navigator
        app.kubernetes.io/name: waf-operator
        version: 0.1.0
    spec:
      affinity: {}
      containers:
      - args:
        - --leader-elect=true
        - --wasm-url=http://tensorsec-holmes-driver.tensorsec.svc.cluster.local:80/waf/waf_filter.wasm
        env:
        - name: MICROSEG_DATASTORE_TYPE
          value: kubernetes
        - name: MICROSEG_POLICY_ENGINE_TYPE
          value: calico
        - name: MY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: MY_POD_APP_LABEl
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['app']
        envFrom:
        - configMapRef:
            name: common-env
        image: harbor.tensorsecurity.com/tensorsecurity/waf-operator:testcn
        imagePullPolicy: Always
        name: waf-operator
        resources:
          limits:
            cpu: 100m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 256Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/secrets/cluster-admin
          name: cluster-admin-secret
          readOnly: true
        - mountPath: /etc/waf
          name: waf-rules
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: harbor-admin-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: tensorsec-waf-operator
      serviceAccountName: tensorsec-waf-operator
      terminationGracePeriodSeconds: 30
      volumes:
      - name: cluster-admin-secret
        secret:
          defaultMode: 420
          secretName: tensorsec-security-operator
      - configMap:
          defaultMode: 420
          name: waf-rules
        name: waf-rules

